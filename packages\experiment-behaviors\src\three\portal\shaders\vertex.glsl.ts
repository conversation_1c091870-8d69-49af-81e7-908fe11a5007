import { noiseShader } from "./noise.glsl";

export const vertexShader = `${noiseShader}

uniform float time;
uniform float rippleNoiseScale;
uniform bool isGhostPass;
uniform float ghostScale;
varying vec3 vNormal;
varying vec3 vViewPos;
varying vec3 vWorldPos;

void main(){
  vNormal = normalMatrix * normal;
  // base position
  vec3 pos = position;
  // Only one noise layer for ripples (finer frequency)
  float displacement = snoise(pos * rippleNoiseScale * 5.0 - time * 0.8) * 0.1 * rippleNoiseScale;
  vec3 displaced = pos + normal * displacement;
  // If ghost pass, scale outward
  if (isGhostPass) {
    displaced *= ghostScale;
  }
  vec4 mvPosition = modelViewMatrix * vec4(displaced, 1.0);
  vViewPos = -mvPosition.xyz;
  vWorldPos = (modelMatrix * vec4(displaced, 1.0)).xyz;
  gl_Position = projectionMatrix * mvPosition;
}`;
