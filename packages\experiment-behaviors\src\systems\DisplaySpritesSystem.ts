import {
  AdditiveBlending,
  NormalBlending,
  Sprite,
  SpriteMaterial,
} from "three";

import { ThreejsContainersSystem } from "@nilo/experiment-behaviors/systems/ThreejsContainersSystem";
import { textureLoadingService } from "@nilo/experiment-behaviors/services/TextureLoadingService";
import { BuiltInComponentTypes } from "@nilo/experiment-behaviors/types/BuiltInComponentData";
import { createMulticaster } from "@nilo/experiment-behaviors/utils/createMulticaster";

import type { EcsSystemContext } from "@nilo/experiment-behaviors/system-context/createSystemContext";
import type { BuiltInComponentData } from "@nilo/experiment-behaviors/types/BuiltInComponentData";
import type {
  EcsSystem,
  EcsSystemApi,
} from "@nilo/experiment-behaviors/types/EcsSystem";
import type { WorldEntityComponentUUID } from "@nilo/experiment-behaviors/types/NilusComponentData";
import type { WorldEntityUUID } from "@nilo/experiment-behaviors/types/NilusEntityData";

const CHILD_KEY_PREFIX = "sprite";

export class DisplaySpritesSystem implements EcsSystem {
  private readonly spriteControllers = new Map<
    WorldEntityComponentUUID,
    SpriteController
  >();

  private readonly onDispose = createMulticaster();

  initialize({ events }: EcsSystemContext) {
    events.onEntityComponentAddedByType.add(
      BuiltInComponentTypes.sprite,
      (context, entityData, componentData) => {
        const containersApi = //
          context.getRequiredSystemApiByConstructor(ThreejsContainersSystem);

        const ctrl = new SpriteController(
          entityData.uuid,
          componentData,
          containersApi
        );
        this.spriteControllers.set(componentData.uuid, ctrl);
      }
    );

    events.onEntityComponentRemovedByType.add(
      BuiltInComponentTypes.sprite,
      (_context, _entityData, componentData) => {
        const ctrl = this.spriteControllers.get(componentData.uuid);
        ctrl?.cleanup();

        this.spriteControllers.delete(componentData.uuid);
      }
    );

    events.onEntityComponentUpdatedByType.add(
      BuiltInComponentTypes.sprite,
      (_context, _entityData, componentData) => {
        const ctrl = this.spriteControllers.get(componentData.uuid);
        ctrl?.onComponentParamsChange(componentData.params);
      }
    );
  }

  dispose() {
    for (const ctrl of this.spriteControllers.values()) {
      ctrl.cleanup();
    }
    this.spriteControllers.clear();
    this.onDispose.invoke();
  }
}

class SpriteController {
  private sprite: Sprite | null = null;

  private readonly childKey: string;

  constructor(
    private readonly entityUuid: WorldEntityUUID,
    private readonly componentData: BuiltInComponentData<
      typeof BuiltInComponentTypes.sprite
    >,
    private readonly containersApi: EcsSystemApi<ThreejsContainersSystem>
  ) {
    this.childKey = `${CHILD_KEY_PREFIX}:${componentData.uuid}`;

    this.createSprite();
  }

  private createSprite() {
    const texture = textureLoadingService.getOrLoadTexture(
      this.componentData.params.textureUri
    );
    const material = new SpriteMaterial({
      map: texture,
      blending:
        this.componentData.params.blendMode === "additive"
          ? AdditiveBlending
          : NormalBlending,
      transparent: true,
      opacity: this.componentData.params.opacity ?? 1.0,
    });
    material.rotation = this.componentData.params.rotation ?? 0;

    this.sprite = new Sprite(material);
    this.sprite.scale.set(
      this.componentData.params.size[0],
      this.componentData.params.size[1],
      1
    );

    this.containersApi.addThreejsChildToEntity(
      this.entityUuid,
      this.sprite,
      this.childKey
    );
  }

  onComponentParamsChange(
    updates: Partial<
      BuiltInComponentData<typeof BuiltInComponentTypes.sprite>["params"]
    >
  ) {
    Object.assign(this.componentData.params, updates);

    // For sprites, it's simpler to just recreate them on any change
    this.cleanup();
    this.createSprite();
  }

  cleanup() {
    if (this.sprite) {
      this.containersApi.removeThreejsChildFromEntity(
        this.entityUuid,
        this.childKey
      );
      this.sprite.material.dispose();
      this.sprite = null;
    }
  }
}
