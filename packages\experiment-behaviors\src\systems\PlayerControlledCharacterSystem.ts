import { createMulticaster } from "@nilo/experiment-behaviors/utils/createMulticaster";
import { BuiltInComponentTypes } from "@nilo/experiment-behaviors/types/BuiltInComponentData";

import type { EcsSystemContext } from "@nilo/experiment-behaviors/system-context/createSystemContext";
import type { EcsSystem } from "@nilo/experiment-behaviors/types/EcsSystem";
import type { WorldEntityUUID } from "@nilo/experiment-behaviors/types/NilusEntityData";
import type { BuiltInComponentData } from "@nilo/experiment-behaviors/types/BuiltInComponentData";

type KeyState = {
  f: boolean; // forward
  l: boolean; // left
  b: boolean; // backward
  r: boolean; // right
};

type ControlConfig = {
  forwardVector: readonly [number, number, number];
  rotationSpeed: number;
  movementSpeed: number;
};

/**
 * Rudimentary player character control system.
 * Mostly for engine testing.
 */
export class PlayerControlledCharacterSystem implements EcsSystem {
  private readonly onDispose = createMulticaster();
  private readonly keyState: KeyState = {
    f: false, // forward
    b: false, // backward
    l: false, // left
    r: false, // right
  };
  private controlledEntityUuid: WorldEntityUUID | null = null;
  private controlConfig: ControlConfig | null = null;

  public readonly events = {
    onControlStarted: createMulticaster<[entityUuid: WorldEntityUUID]>(),
    onControlEnded: createMulticaster<[entityUuid: WorldEntityUUID]>(),
  };

  initialize(context: EcsSystemContext) {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!this.controlledEntityUuid) return;

      switch (event.key.toLowerCase()) {
        case "w":
          this.keyState.f = true;
          break;
        case "a":
          this.keyState.l = true;
          break;
        case "s":
          this.keyState.b = true;
          break;
        case "d":
          this.keyState.r = true;
          break;
      }
    };

    const handleKeyUp = (event: KeyboardEvent) => {
      if (!this.controlledEntityUuid) return;

      switch (event.key.toLowerCase()) {
        case "w":
          this.keyState.f = false;
          break;
        case "a":
          this.keyState.l = false;
          break;
        case "s":
          this.keyState.b = false;
          break;
        case "d":
          this.keyState.r = false;
          break;
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    window.addEventListener("keyup", handleKeyUp);

    this.onDispose.add(() => {
      window.removeEventListener("keydown", handleKeyDown);
      window.removeEventListener("keyup", handleKeyUp);
    });

    context.events.onFrame.add((_context, deltaTime) => {
      if (!this.controlledEntityUuid || !this.controlConfig) return;

      const entityDataProxy = context.getEntityDataMutationProxy(
        this.controlledEntityUuid
      );
      if (!entityDataProxy) return;

      const [dx, dy] = this.calculateMovementVector(deltaTime);
      if (dx === 0 && dy === 0) return;

      // Update position
      const [x, y, z] = entityDataProxy.transform.position;
      const distance = this.controlConfig.movementSpeed * deltaTime;
      entityDataProxy.transform.position = [
        x + dx * distance,
        y,
        z + dy * distance,
      ];

      // Calculate target rotation based on movement direction and forward vector
      const movementAngle = Math.atan2(-dx, -dy);
      const forwardAngle = Math.atan2(
        -this.controlConfig.forwardVector[0],
        -this.controlConfig.forwardVector[2]
      );
      const targetRotation = movementAngle - forwardAngle;

      // Update rotation
      const [rx, ry, rz] = entityDataProxy.transform.rotation;
      const currentRotation = ry;
      const rotationDelta = this.shortestAngleBetween(
        currentRotation,
        targetRotation
      );
      const maxRotationDelta = this.controlConfig.rotationSpeed * deltaTime;
      const clampedRotationDelta =
        Math.sign(rotationDelta) *
        Math.min(Math.abs(rotationDelta), maxRotationDelta);

      entityDataProxy.transform.rotation = [
        rx,
        currentRotation + clampedRotationDelta,
        rz,
      ];
    });
  }

  private shortestAngleBetween(a: number, b: number): number {
    const diff = ((b - a + Math.PI) % (Math.PI * 2)) - Math.PI;
    return diff < -Math.PI ? diff + Math.PI * 2 : diff;
  }

  private calculateMovementVector(_deltaTime: number): [number, number] {
    const { f, l, b, r } = this.keyState;
    if (!f && !l && !b && !r) return [0, 0];

    let dx = 0;
    let dy = 0;

    if (f) dy -= 1;
    if (b) dy += 1;
    if (l) dx -= 1;
    if (r) dx += 1;

    // Normalize diagonal movement
    if (dx !== 0 && dy !== 0) {
      const length = Math.sqrt(dx * dx + dy * dy);
      dx /= length;
      dy /= length;
    }

    return [dx, dy];
  }

  public getApi(context: EcsSystemContext) {
    return {
      setControlledEntity: (entityUuid: WorldEntityUUID) => {
        if (this.controlledEntityUuid === entityUuid) return;

        // Check if entity has the required component
        const component = context.getEntityComponentsDataOfType(
          entityUuid,
          BuiltInComponentTypes.playerControlledCharacter
        )[0];

        if (!component) {
          throw new Error(
            `🎮 Entity ${entityUuid} must have a playerControlledCharacter component to be controlled`
          );
        }

        if (this.controlledEntityUuid) {
          this.events.onControlEnded.invoke(this.controlledEntityUuid);
        }

        // Convert component params to control config
        this.controlConfig = {
          forwardVector: directionStringToVector(
            component.params.forwardVector
          ),
          rotationSpeed: component.params.rotationSpeed,
          movementSpeed: component.params.movementSpeed,
        };

        this.controlledEntityUuid = entityUuid;
        this.events.onControlStarted.invoke(entityUuid);
        console.debug(
          `🎮 Now controlling entity ${entityUuid} with config:`,
          this.controlConfig
        );
      },

      cancelCurrentControl: () => {
        if (!this.controlledEntityUuid) return;

        const previousUuid = this.controlledEntityUuid;
        this.controlledEntityUuid = null;
        this.controlConfig = null;
        this.events.onControlEnded.invoke(previousUuid);
        console.debug(`🎮 Stopped controlling entity ${previousUuid}`);
      },

      getControlledEntity: () => this.controlledEntityUuid,

      events: this.events,
    };
  }

  dispose() {
    if (this.controlledEntityUuid) {
      this.events.onControlEnded.invoke(this.controlledEntityUuid);
    }
    this.controlledEntityUuid = null;
    this.controlConfig = null;
    this.events.onControlStarted.clear();
    this.events.onControlEnded.clear();
    this.onDispose.invoke();
    console.debug("🧹 PlayerControlledCharacterSystem: Disposed");
  }
}

function directionStringToVector(
  direction: BuiltInComponentData<
    typeof BuiltInComponentTypes.playerControlledCharacter
  >["params"]["forwardVector"]
): readonly [number, number, number] {
  switch (direction) {
    case "forward":
      return [0, 0, -1];
    case "backward":
      return [0, 0, 1];
    case "left":
      return [-1, 0, 0];
    case "right":
      return [1, 0, 0];
  }
}
