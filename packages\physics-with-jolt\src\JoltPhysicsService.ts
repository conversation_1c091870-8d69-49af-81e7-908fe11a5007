import Jolt from "jolt-physics";

import { createDelegateFunction, DelegateFunction } from "@nilo/utilities";

import { BodyControl } from "./controller/BodyControl";
import { BodyControlFactory } from "./factories/BodyControlFactory";
import { createContactsService } from "./factories/ContactsService";
import {
  LAYER_CHARACTER_COLLIDERS,
  LAYER_CHARACTER_SHAPE,
  LAYER_MOVING,
  LAYER_NON_MOVING,
  NUM_OBJECT_LAYERS,
} from "./lib/constants";
import { PhysicsOptions, PhysicsShapeType } from "./types/PhysicsOptions";
import { RagdollSettings } from "./types/Ragdoll";
import {
  RagdollControl,
  RagdollControlFactory,
} from "./factories/RagdollControlFactory";
import { BodyGeometry, PhysicsBody, PhysicsBodyUpdate } from "./types/Body";

type IdBodyControlPair = [string, BodyControl];

export type PhysicsWorldDelegates = {
  readonly beforeFrameUpdate: DelegateFunction<[]>;
  readonly afterFrameUpdate: DelegateFunction<[]>;
  readonly afterObjectBodyAdded: DelegateFunction<
    [object: PhysicsBody, bodyCtrl: BodyControl]
  >;
  readonly afterObjectBodyRemoved: DelegateFunction<
    [object: PhysicsBody, bodyCtrl: BodyControl]
  >;
  readonly afterObjectChanged: DelegateFunction<
    [object: PhysicsBody, bodyCtrl: BodyControl]
  >;
  readonly startLoop: DelegateFunction<[]>;
  readonly stopLoop: DelegateFunction<[]>;
  readonly bodyChanged: DelegateFunction<
    [object: PhysicsBody, bodyID: Jolt.BodyID]
  >;
};
export async function createJoltPhysicsService() {
  const dirtyBodies = new Set<PhysicsBodyUpdate>();

  const delegates: PhysicsWorldDelegates = {
    beforeFrameUpdate: createDelegateFunction(),
    afterFrameUpdate: createDelegateFunction(),
    afterObjectBodyAdded:
      createDelegateFunction<[object: PhysicsBody, bodyCtrl: BodyControl]>(),
    afterObjectBodyRemoved:
      createDelegateFunction<[object: PhysicsBody, bodyCtrl: BodyControl]>(),
    afterObjectChanged:
      createDelegateFunction<[object: PhysicsBody, bodyCtrl: BodyControl]>(),
    startLoop: createDelegateFunction(),
    stopLoop: createDelegateFunction(),
    bodyChanged:
      createDelegateFunction<[object: PhysicsBody, bodyId: Jolt.BodyID]>(),
  } as PhysicsWorldDelegates;

  const joltModule = await Jolt();
  const freeMemoryAfterJoltModuleLoad = getFreeMemory();
  // let lastMeasurement: number = freeMemoryAfterJoltModuleLoad;
  let freeMemoryBeforeSettingsCreated: number = -1;

  console.debug("🎈 Jolt Physics Service Created");

  function initJoltPhysics() {
    freeMemoryBeforeSettingsCreated = getFreeMemory();
    const settings = new joltModule.JoltSettings();

    // Set up collision filtering
    const objectFilter = new joltModule.ObjectLayerPairFilterTable(
      NUM_OBJECT_LAYERS
    );
    objectFilter.EnableCollision(LAYER_NON_MOVING, LAYER_MOVING);
    objectFilter.EnableCollision(LAYER_MOVING, LAYER_MOVING);
    objectFilter.EnableCollision(LAYER_CHARACTER_COLLIDERS, LAYER_MOVING);
    objectFilter.EnableCollision(LAYER_CHARACTER_COLLIDERS, LAYER_NON_MOVING);
    objectFilter.EnableCollision(
      LAYER_CHARACTER_COLLIDERS,
      LAYER_CHARACTER_COLLIDERS
    );
    objectFilter.EnableCollision(LAYER_CHARACTER_SHAPE, LAYER_NON_MOVING);
    objectFilter.EnableCollision(LAYER_CHARACTER_SHAPE, LAYER_MOVING);
    objectFilter.EnableCollision(LAYER_CHARACTER_SHAPE, LAYER_CHARACTER_SHAPE);

    const BP_LAYER_NON_MOVING = new joltModule.BroadPhaseLayer(0);
    const BP_LAYER_MOVING = new joltModule.BroadPhaseLayer(1);
    const NUM_BROAD_PHASE_LAYERS = 2;

    const bpInterface = new joltModule.BroadPhaseLayerInterfaceTable(
      NUM_OBJECT_LAYERS,
      NUM_BROAD_PHASE_LAYERS
    );
    bpInterface.MapObjectToBroadPhaseLayer(
      LAYER_NON_MOVING,
      BP_LAYER_NON_MOVING
    );
    bpInterface.MapObjectToBroadPhaseLayer(LAYER_MOVING, BP_LAYER_MOVING);
    bpInterface.MapObjectToBroadPhaseLayer(
      LAYER_CHARACTER_COLLIDERS,
      BP_LAYER_MOVING
    );
    bpInterface.MapObjectToBroadPhaseLayer(
      LAYER_CHARACTER_SHAPE,
      BP_LAYER_MOVING
    );
    settings.mObjectLayerPairFilter = objectFilter;
    settings.mBroadPhaseLayerInterface = bpInterface;
    settings.mObjectVsBroadPhaseLayerFilter =
      new joltModule.ObjectVsBroadPhaseLayerFilterTable(
        settings.mBroadPhaseLayerInterface,
        NUM_BROAD_PHASE_LAYERS,
        settings.mObjectLayerPairFilter,
        NUM_OBJECT_LAYERS
      );
    const joltInterface = new joltModule.JoltInterface(settings);
    const physicsSystem = joltInterface.GetPhysicsSystem();
    const bodyInterface = physicsSystem.GetBodyInterface();

    joltModule.destroy(settings);

    return {
      joltInterface,
      physicsSystem,
      bodyInterface,
      BP_LAYER_NON_MOVING,
      BP_LAYER_MOVING,
    };
  }

  const {
    joltInterface,
    physicsSystem,
    bodyInterface,
    BP_LAYER_NON_MOVING,
    BP_LAYER_MOVING,
  } = initJoltPhysics();

  const contactsService = createContactsService(joltModule);
  physicsSystem.SetContactListener(contactsService.listener);

  const bodyControlFactory = new BodyControlFactory(
    joltModule,
    bodyInterface,
    delegates,
    contactsService
  );

  const ragdollControlFactory = new RagdollControlFactory(
    joltModule,
    physicsSystem,
    bodyControlFactory
  );

  const characterVirtualBodies = new Set<Jolt.CharacterVirtual>();

  const objectBodiesDictionary = new Map<string, BodyControl>();
  const objectBodiesDictionaryEntries: IdBodyControlPair[] = [];
  let objectBodiesDictionaryEntriesDirty = true;

  function _updateBodiesDictionaryEntriesIfDirty() {
    if (!objectBodiesDictionaryEntriesDirty) {
      return;
    }

    objectBodiesDictionaryEntriesDirty = false;
    objectBodiesDictionaryEntries.length = 0;
    const entries = [...objectBodiesDictionary.entries()];
    objectBodiesDictionaryEntries.push(...entries);
  }

  let staticBodiesCount = 0;
  let dynamicBodiesCount = 0;
  const updateBodiesCount = () => {
    const bodyControls = [...objectBodiesDictionary.values()];
    for (const bodyControl of bodyControls) {
      try {
        if (bodyControl.getStatic()) {
          staticBodiesCount++;
        } else {
          dynamicBodiesCount++;
        }
      } catch (error) {
        console.warn("💥 Error in updateBodiesCount", { error });
        continue;
      }
    }
  };

  let runningPhysicsLoop = false;
  function startPhysicsLoop() {
    if (runningPhysicsLoop) {
      console.warn("⚠️ Physics loop is already running");
      return;
    }
    runningPhysicsLoop = true;
    delegates.startLoop.invoke();
  }

  function physicsStep() {
    if (dynamicBodiesCount <= 0 || !runningPhysicsLoop) {
      return;
    }

    try {
      delegates.beforeFrameUpdate.invoke();

      updatePhysics(1 / 60);

      delegates.afterFrameUpdate.invoke();
    } catch (error) {
      console.error("💥 Error in physics loop", { error });
      stopPhysicsLoop();
    }
  }

  function stopPhysicsLoop() {
    if (!runningPhysicsLoop) {
      console.warn("⚠️ Physics loop is already stopped");
      return;
    }
    runningPhysicsLoop = false;
    delegates.stopLoop.invoke();
  }

  async function registerRagdollForPhysics(
    ragdollSettings: RagdollSettings
  ): Promise<RagdollControl> {
    const ragdollControl = ragdollControlFactory.createRagdoll(ragdollSettings);

    ragdollControl.ragdoll.AddToPhysicsSystem(Jolt.EActivation_Activate);

    return ragdollControl;
  }

  const defaultPhysicsOptions: PhysicsOptions = {
    isStatic: false,
    shapeType: "convexHull",
    friction: 0.5,
    restitution: 0.05,
  };
  async function registerObjectForPhysics(
    physicsBody: PhysicsBody,
    geometry: BodyGeometry
  ): Promise<BodyControl | undefined> {
    const physicsOptions: PhysicsOptions = {
      ...defaultPhysicsOptions,
      ...physicsBody.physicsOptions,
    };
    const bodyControl = await bodyControlFactory.create(
      physicsBody,
      geometry,
      physicsOptions
    );
    if (!bodyControl) {
      console.warn("💛 No body control found for object:", physicsBody.id);
      return;
    }

    objectBodiesDictionary.set(physicsBody.id, bodyControl);
    objectBodiesDictionaryEntriesDirty = true;
    updateBodiesCount();
    return bodyControl;

    // (window as any).debugFreeMemory("registerObjectForPhysics B");
  }

  function unregisterObjectFromPhysics(id: string) {
    const bodyControl = objectBodiesDictionary.get(id);
    if (!bodyControl) {
      console.error("Body not found in objectsDictionary");
      return;
    }
    bodyControl.destroyBody();

    objectBodiesDictionary.delete(id);
    objectBodiesDictionaryEntriesDirty = true;
    updateBodiesCount();
    return bodyControl;
  }

  function unregisterRagdollFromPhysics(ragdollControl: RagdollControl) {
    ragdollControl.dispose();
  }

  const initialGravity = physicsSystem.GetGravity();
  const _zeroGravity = new joltModule.Vec3(0, 0, 0);
  function setGravityEnabled(enabled: boolean) {
    if (enabled) {
      physicsSystem.SetGravity(initialGravity);
    } else {
      physicsSystem.SetGravity(_zeroGravity);
    }
  }

  const isLoggingPhysicsUpdate = false;
  const updateTimes: number[] = [];
  let updateTimesTotal = 0;
  let totalFrames = 0;
  function updatePhysics(deltaTime: number) {
    const startTime = isLoggingPhysicsUpdate ? performance.now() : 0;

    // When running below 55 Hz, do 2 steps instead of 1
    const numSteps = deltaTime > 1.0 / 55.0 ? 2 : 1;
    joltInterface.Step(deltaTime, numSteps);

    //Handle collisions/contacts
    contactsService.broadcastContacts();

    _updateBodiesDictionaryEntriesIfDirty();
    for (let i = 0; i < objectBodiesDictionaryEntries.length; i++) {
      const pair = objectBodiesDictionaryEntries[i];
      const obj = pair[0];
      const bodyControl = pair[1];
      if (bodyControl.shouldPauseUpdatesFromPhysics) {
        continue;
      }

      const body = bodyControl.getBodyRaw();
      if (!body) {
        continue;
      }

      const bodyPosition = body.GetPosition();
      const bodyRotation = body.GetRotation();

      const worldPosition = {
        x: bodyPosition.GetX(),
        y: bodyPosition.GetY(),
        z: bodyPosition.GetZ(),
      };
      const worldRotation = {
        x: bodyRotation.GetX(),
        y: bodyRotation.GetY(),
        z: bodyRotation.GetZ(),
        w: bodyRotation.GetW(),
      };
      dirtyBodies.add({
        id: obj,
        position: worldPosition,
        rotation: worldRotation,
      });
    }

    if (isLoggingPhysicsUpdate) {
      const endTime = performance.now();
      const updateTime = endTime - startTime;
      updateTimesTotal += updateTime;
      updateTimes.push(updateTime);
      totalFrames++;
      const FRAMES_TO_MEASURE = 60;
      if (updateTimes.length > FRAMES_TO_MEASURE) {
        updateTimes.shift();
      }

      if (updateTimes.length === FRAMES_TO_MEASURE) {
        const averageTime =
          updateTimes.reduce((a, b) => a + b, 0) / FRAMES_TO_MEASURE;
        console.debug(
          `Average physics update over last ${FRAMES_TO_MEASURE} frames: ${averageTime.toFixed(2)}ms for ${
            objectBodiesDictionaryEntries.length
          } objects. TotalAverage: ${updateTimesTotal / totalFrames}ms.`
        );
      }
    }
  }
  function getStaticBodiesCount() {
    return staticBodiesCount;
  }
  function getDynamicBodiesCount() {
    return dynamicBodiesCount;
  }
  function getFreeMemory(): number {
    return joltModule.JoltInterface.prototype.sGetFreeMemory() ?? Infinity;
  }

  // function debugFreeMemory(label: string) {
  //   const currentFreeMemory = getFreeMemory();
  //   const usedMemory = freeMemoryAfterJoltModuleLoad - currentFreeMemory;
  //   const usedMemorySinceLastMeasurement = lastMeasurement - currentFreeMemory;
  //   console.debug("💥💥💥", label, usedMemory, usedMemorySinceLastMeasurement);
  //   lastMeasurement = currentFreeMemory;
  // }
  // (window as any).debugFreeMemory = debugFreeMemory;
  // debugFreeMemory("init");

  function destroy() {
    for (const body of objectBodiesDictionary.values()) {
      body.destroyBody();
    }
    objectBodiesDictionary.clear();

    // for (const body of characterVirtualBodies) {
    //   // TODO: Figure out how to destroy character virtual bodies
    //   body.destroy();
    // }
    characterVirtualBodies.clear();

    bodyControlFactory.destroy();
    joltModule.destroy(_zeroGravity);
    joltModule.destroy(joltInterface);
    joltModule.destroy(contactsService.listener);
    joltModule.destroy(BP_LAYER_NON_MOVING);
    joltModule.destroy(BP_LAYER_MOVING);
  }

  async function findBestShape(
    geometry: BodyGeometry
  ): Promise<PhysicsShapeType> {
    return bodyControlFactory.findBestShape(geometry);
  }

  ////////////////////////////////////////////
  ////////////////////////////////////////////
  // delegates.afterObjectBodyRemoved.on((object, bodyCtrl) => {
  //   stopPhysicsLoop();
  //   console.debug("💥 Body removed", object.name);
  //   console.debug("💥 Current state", {
  //     animationFrameId,
  //     dynamicBodiesCount,
  //   });
  //   console.debug("💥 Current objects", {
  //     objectBodiesDictionary: [...objectBodiesDictionary.keys()],
  //   });
  // });
  ////////////////////////////////////////////
  ////////////////////////////////////////////

  return {
    delegates,
    contacts: contactsService,

    startPhysicsLoop,
    stopPhysicsLoop,
    isPhysicsLoopRunning: () => runningPhysicsLoop,

    getBodyControlById: (id: string) => objectBodiesDictionary.get(id)!,
    getObjectBodyControlPairs: () => [...objectBodiesDictionaryEntries],

    registerRagdollForPhysics,
    registerObjectForPhysics,
    unregisterObjectFromPhysics,
    unregisterRagdollFromPhysics,
    destroy,
    findBestShape,

    setGravityEnabled,
    getStaticBodiesCount,
    getDynamicBodiesCount,

    //// TEMP
    //// TODO: Tidy up and expose proper methods for this
    characterVirtualBodies,

    memoryStats: {
      freeMemoryAfterJoltModuleLoad,
      freeMemoryBeforeSettingsCreated,
    },

    getAndClearDirtyBodies: () => {
      const dirtyBodiesArray = [...dirtyBodies];
      dirtyBodies.clear();
      return dirtyBodiesArray;
    },
    physicsStep,
    /**

     * Direct references to Jolt-specific modules and objects.
     *
     * Ideally, once we're done experimenting all the time here,
     * we should remove these without breaking anything.
     */
    joltInternals: {
      joltModule,
      joltInterface,
      physicsSystem,
      bodyInterface,
      getFreeMemory,
    },
  };
}

export type JoltPhysicsService = Awaited<
  ReturnType<typeof createJoltPhysicsService>
>;
