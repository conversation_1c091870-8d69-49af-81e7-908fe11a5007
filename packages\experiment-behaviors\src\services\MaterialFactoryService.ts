import {
  Color,
  ColorRepresentation,
  Material,
  MeshPhysicalMaterial,
  MeshStandardMaterial,
} from "three";

type MaterialPreset = "metal" | "glass";
type MaterialType = MeshStandardMaterial | MeshPhysicalMaterial;

interface MaterialPool<T extends Material> {
  materials: Map<string, T>;
  createMaterial: (color: ColorRepresentation) => T;
}

const createMaterialPool = <T extends Material>(
  instantiate: (color: ColorRepresentation) => T
): MaterialPool<T> => {
  const materials = new Map<string, T>();

  const createMaterial = (color: ColorRepresentation): T => {
    const colorKey = new Color(color).getHexString();
    const existing = materials.get(colorKey);
    if (existing) {
      return existing;
    }

    const newMaterial = instantiate(color);
    materials.set(colorKey, newMaterial);
    return newMaterial;
  };

  return {
    materials,
    createMaterial,
  };
};

// Material factory functions
const materialFactories = {
  metal: (color: ColorRepresentation): MeshStandardMaterial =>
    new MeshStandardMaterial({
      color,
      metalness: 0.9,
      roughness: 0.4,
    }),

  glass: (color: ColorRepresentation): MeshPhysicalMaterial => {
    const lerpedColor = new Color(color).lerp(new Color("white"), 0.1);
    return new MeshPhysicalMaterial({
      color: lerpedColor,
      metalness: 0.0,
      roughness: 0.1,
      transmission: 1.0,
      ior: 1.35,
      thickness: 0.9,
      transparent: true,
      opacity: 1.0,
      clearcoat: 1.0,
      clearcoatRoughness: 0.0,
      envMapIntensity: 2,
      specularIntensity: 0.8,
      specularColor: color,
    });
  },
} as const;

export const createMaterialFactoryService = () => {
  const pools = new Map<MaterialPreset, MaterialPool<MaterialType>>([
    ["metal", createMaterialPool(materialFactories.metal)],
    ["glass", createMaterialPool(materialFactories.glass)],
  ]);

  const getMaterial = (
    preset: MaterialPreset,
    color: ColorRepresentation
  ): MaterialType => {
    const pool = pools.get(preset);
    if (!pool) {
      throw new Error(`🎨 No material pool found for preset: ${preset}`);
    }
    return pool.createMaterial(color);
  };

  const dispose = () => {
    for (const pool of pools.values()) {
      for (const material of pool.materials.values()) {
        material.dispose();
      }
      pool.materials.clear();
    }
    pools.clear();
  };

  // Debug helpers
  const getPoolStats = () => {
    const stats = new Map<MaterialPreset, number>();
    for (const [preset, pool] of pools.entries()) {
      stats.set(preset, pool.materials.size);
    }
    return Object.fromEntries(stats);
  };

  return {
    getMaterial,
    dispose,
    getPoolStats,
  };
};

// Type exports
export type { MaterialPreset, MaterialType };
