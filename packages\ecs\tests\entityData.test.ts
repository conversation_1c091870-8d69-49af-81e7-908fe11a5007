import { expect, test } from "@jest/globals";
import { World } from "../src/world";
import { Query } from "../src/query";
import { EntityData } from "../src/entityData";
import { Position, Velocity } from "./common";

test("EntityData", () => {
  const world = new World();
  const entity = world.addEntity();

  const entityData = world.entityData(entity);

  expect(entityData).not.toBeNull();

  entityData?.addComponent(Position, { x: 0, y: 0 });
  entityData?.addComponent(Velocity, { x: 0, y: 0 });

  expect(entityData?.getComponent(Position)).toEqual({ x: 0, y: 0 });
  expect(entityData?.getComponent(Velocity)).toEqual({ x: 0, y: 0 });

  entityData?.removeComponent(Position);

  expect(entityData?.getComponent(Position)).toBeNull();

  entityData?.addComponent(Position, { x: 1, y: 1 });

  expect(entityData?.getComponent(Position)).toEqual({ x: 1, y: 1 });

  entityData?.addComponents([[Position, { x: 2, y: 2 }]]);

  expect(entityData?.getComponent(Position)).toEqual({ x: 2, y: 2 });

  entityData?.removeComponent(Position);

  expect(entityData?.getComponent(Position)).toBeNull();

  world.removeEntity(entity);

  expect(entityData!.isAlive()).toBe(false);
});

test("EntityData components", () => {
  const world = new World();
  const entity = world.addEntity();

  const entityData = world.entityData(entity);

  expect(entityData).toBeDefined();

  expect(entityData?.allComponents()).toEqual([]);

  entityData?.addComponent(Position, { x: 10, y: 20 });
  entityData?.addComponent(Velocity, { x: 5, y: -3 });

  expect(entityData?.allComponents()).toEqual([
    [Position, { x: 10, y: 20 }],
    [Velocity, { x: 5, y: -3 }],
  ]);

  entityData?.removeComponent(Position);

  const remainingComponents = entityData?.allComponents();
  expect(remainingComponents).toEqual([[Velocity, { x: 5, y: -3 }]]);
});

test("EntityData isDeferred - normal entity", () => {
  const world = new World();
  const entity = world.addEntity([[Position, { x: 1, y: 2 }]]);
  const entityData = world.entityData(entity);

  expect(entityData?.isDeferred()).toBe(false);
  expect(entityData?.hasComponent(Position)).toBe(true);
});

test("EntityData isDeferred - during query", () => {
  const world = new World();
  world.addEntity([[Position, { x: 1, y: 2 }]]);

  const query = new Query({ pos: Position });
  let deferredEntityData: EntityData | null = null;

  query.forEach(world, (_entity, _data) => {
    const deferredEntity = world.addEntity([[Position, { x: 10, y: 20 }]]);
    deferredEntityData = world.entityData(deferredEntity);

    expect(deferredEntityData?.isDeferred()).toBe(true);
    expect(deferredEntityData?.hasComponent(Position)).toBe(false);
  });

  expect(deferredEntityData!.isDeferred()).toBe(false);
  expect(deferredEntityData!.hasComponent(Position)).toBe(true);
});

test("EntityData isDeferred - component operations", () => {
  const world = new World();
  world.addEntity([[Position, { x: 1, y: 2 }]]);

  const query = new Query({ pos: Position });
  let deferredEntityData: EntityData | null = null;

  query.forEach(world, (_entity, _data) => {
    const deferredEntity = world.addEntity([[Position, { x: 5, y: 10 }]]);
    deferredEntityData = world.entityData(deferredEntity);

    deferredEntityData?.addComponent(Velocity, { x: 1, y: 1 });
    expect(deferredEntityData?.hasComponent(Velocity)).toBe(false);
    expect(deferredEntityData?.allComponents()).toEqual([]);
  });

  expect(deferredEntityData!.hasComponent(Position)).toBe(true);
  expect(deferredEntityData!.hasComponent(Velocity)).toBe(true);
});
