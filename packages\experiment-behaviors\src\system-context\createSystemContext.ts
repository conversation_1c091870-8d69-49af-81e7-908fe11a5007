import type { EcsEngineEventsWithoutInvoke } from "../createEcsEngineEvents";
import type { BuiltInComponentType } from "../types/BuiltInComponentData";
import type {
  EntitiesDataController,
  ErrorsController,
} from "../types/EcsEngineControllers";
import type { EcsSystem } from "../types/EcsSystem";
import type { WorldEntityComponentUUID } from "../types/NilusComponentData";
import type { WorldEntityUUID } from "../types/NilusEntityData";

type SystemConstructor<T extends EcsSystem> = new (...args: any[]) => T;

type SystemContextScope = {
  entityUuid?: WorldEntityUUID;
  componentUuid?: WorldEntityComponentUUID;
  systemName?: string;
  during?: string;
};

type SystemApi<T extends EcsSystem> = T["getApi"] extends (
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  context: any
) => infer R
  ? R
  : undefined;

//// TODO: Implement pooling, so that we don't really reacreate the context every time we need it.
export function createEcsSystemContext(
  systems: EcsSystem[],
  scope: SystemContextScope,
  entitiesDataController: EntitiesDataController,
  errorsController: ErrorsController,
  events: EcsEngineEventsWithoutInvoke
) {
  const hasThisSystemConstructor = <T extends EcsSystem>(
    system: EcsSystem,
    SystemConstructor: new (...args: any[]) => T
  ): system is T => {
    return system instanceof SystemConstructor;
  };

  const findSystemByConstructor = <T extends EcsSystem>(
    SysConstr: SystemConstructor<T>
  ): T | null => {
    const system = systems.find((s) => hasThisSystemConstructor(s, SysConstr));
    return system ?? null;
  };

  return {
    events,

    getEntitiesData: () => {
      return entitiesDataController.getAllEntities();
    },

    ////
    //// ACCESS SYSTEMS
    ////

    // getSystemByConstructorName: (name: string) =>
    //   systems.find((system) => system.constructor.name === name),

    getRequiredSystemApiByConstructor<T extends EcsSystem>(
      SysConstr: SystemConstructor<T>
    ) {
      const system = findSystemByConstructor(SysConstr);
      if (!system) {
        throw new Error(`Required system ${SysConstr.name} not found`);
      }

      const api = system.getApi?.(this) as SystemApi<T>;
      if (!api) {
        throw new Error(`System ${SysConstr.name} has no .getApi() dfined`);
      }

      return api;
    },

    ////
    //// ACCESS / MUTATE ENTITIES
    ////

    getEntityData: (entityUuid: WorldEntityUUID) => {
      return entitiesDataController.getEntityData(entityUuid);
    },
    getEntityDataMutationProxy: (entityUuid: WorldEntityUUID) => {
      return entitiesDataController.getEntityDataMutationProxy(entityUuid);
    },
    getEntityComponentsDataOfType: <K extends BuiltInComponentType>(
      entityUuid: WorldEntityUUID,
      componentType: K
    ) => {
      return entitiesDataController.getEntityComponentsDataOfType(
        entityUuid,
        componentType
      );
    },

    ////
    //// HANDLE ERRORS
    ////

    handleError: (error: unknown, message?: string) => {
      if (!scope.entityUuid) {
        console.error(
          "💀 Handling global Nilus error, no entity UUID provided",
          error
        );
        return;
      }

      const errorMessage =
        message ?? (error instanceof Error ? error.message : String(error));

      errorsController.addError({
        message: errorMessage,
        entityUuid: scope.entityUuid,
        componentUuid: scope.componentUuid,
        systemName: scope.systemName,
        during: scope.during,
        handled: true,
      });

      console.error(`🔴 ${message}`);
    },

    clearEntityErrors: (entityUuid: WorldEntityUUID) => {
      errorsController.clearErrors(entityUuid);
    },
  };
}

export type EcsSystemContext = ReturnType<typeof createEcsSystemContext> & {};
