const smoothstep = (x: number) => {
  x = Math.max(0, Math.min(1, x));
  return x * x * (3 - 2 * x);
};

export const createTextureCycler = (urls: string[]) => {
  let imageIndex = 0;
  let blendFactor = 0;
  let timeScale = 1;

  return {
    setPeriod: (period: number) => {
      timeScale = 1 / period;
    },
    onFrame: (deltaTime: number) => {
      blendFactor += deltaTime * timeScale;

      if (blendFactor >= 1) {
        blendFactor = 0;
        imageIndex = (imageIndex + 1) % urls.length;
      }

      const url1 = urls[imageIndex];
      const url2 = urls[(imageIndex + 1) % urls.length];

      return {
        blendFactor: smoothstep(blendFactor),
        url1,
        url2,
      };
    },
  };
};
