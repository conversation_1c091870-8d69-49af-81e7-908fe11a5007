import { Animation<PERSON>lip, Group, Loader, LoadingManager, Object3D } from "three";
import { GLTFLoader } from "three/examples/jsm/loaders/GLTFLoader";
import { DRACOLoader } from "three/examples/jsm/loaders/DRACOLoader";

import { animationCacheService } from "./AnimationCacheService";
import { primitiveFactoryService } from "./PrimitiveFactoryService";

import type { ModelURI } from "@nilo/experiment-behaviors/types/BuiltInComponentData";

const globalLoadingManager = new LoadingManager();

type AnimationCacheEntry = {
  sourceUri: string;
  sourceUuid: string;
  animations: AnimationClip[];
};

function createModelLoadingService() {
  ////// Pre-defining here, so that the instance can be reused
  const gltfLoader = new GLTFLoader(globalLoadingManager);
  const dracoLoader = createDracoLoader();
  gltfLoader.setDRACOLoader(dracoLoader);

  const loadersPerExtension: Record<string, Loader> = {
    gltf: gltfLoader,
    glb: gltfLoader,
  };

  const animationCache: AnimationCacheEntry[] = [];

  async function loadModel(uri: ModelURI) {
    try {
      const loadingResult = await loadModelFromUri(uri, loadersPerExtension);
      if (!loadingResult) {
        throw new Error("🔴 Failed to load model for URI: " + uri);
      }

      const modelObject = getModelThreejsObject(loadingResult);
      if (!modelObject) {
        throw new Error("🔴 Failed to get model object for URI: " + uri);
      }

      const animations = getModelAnimations(loadingResult);
      if (animations.length > 0) {
        animationCacheService.cacheAnimations(uri, modelObject, animations);
      }

      return modelObject;
    } catch (error) {
      console.error("🔴 Failed to load model:", error);
      throw error;
    }
  }

  function getAnimationsForUri(uri: string): AnimationClip[] {
    const entry = animationCache.find((entry) => entry.sourceUri === uri);
    return entry?.animations ?? [];
  }

  function getAnimationsForUuid(uuid: string): AnimationClip[] {
    const entry = animationCache.find((entry) => entry.sourceUuid === uuid);
    return entry?.animations ?? [];
  }

  return {
    loadModel,
    getAnimationsForUri,
    getAnimationsForUuid,
  };
}

function createDracoLoader() {
  const url = "https://www.gstatic.com/draco/v1/decoders/";
  const dracoLoader = new DRACOLoader();
  dracoLoader.setDecoderPath(url);
  return dracoLoader;
}

export type ModelLoadingService = ReturnType<typeof createModelLoadingService>;

//// Export singleton instance
export const modelLoadingService = createModelLoadingService();

////////////////////////////////////////////////////////////
// Helper functions (this all should go to separate file(s) eventually)
////////////////////////////////////////////////////////////

async function loadModelFromUri(
  uri: ModelURI,
  loadersPerExtension: Record<string, Loader>
): Promise<unknown> {
  if (primitiveFactoryService.isPrimitiveUri(uri)) {
    return primitiveFactoryService.createPrimitive(uri);
  }

  const extension = uri.split(".").pop()?.toLowerCase();
  if (!extension) {
    throw new Error("🔴 Failed to get extension for URI: " + uri);
  }

  const loader = loadersPerExtension[extension];
  if (!loader) {
    throw new Error("🔴 No loader found for extension: " + extension);
  }

  const model = await loader.loadAsync(uri);
  return model as unknown & { scene?: unknown };
}

function getModelThreejsObject(model: unknown): Object3D | null {
  if (model instanceof Object3D) {
    return model;
  }

  if (model instanceof Object && hasSceneProperty(model)) {
    return model.scene;
  }

  return null;
}

function getModelAnimations(model: unknown): AnimationClip[] {
  if (
    model instanceof Object &&
    "animations" in model &&
    Array.isArray(model.animations)
  ) {
    return model.animations;
  }
  return [];
}

function hasSceneProperty<T extends object>(
  model: T
): model is T & { scene: Group } {
  return "scene" in model && model.scene instanceof Group;
}
