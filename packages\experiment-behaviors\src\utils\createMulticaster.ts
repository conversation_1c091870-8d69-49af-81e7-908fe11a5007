export function createMulticaster<Params extends unknown[], R = void>() {
  type Callback = (...args: Params) => R;

  const callbacks: Callback[] = [];

  let handleError:
    | ((error: unknown, cbIndex: number, cb: Callback) => void)
    | undefined;

  return {
    /**
     * Adds a callback to be invoked when the multicaster is triggered.
     * @param {Callback} callback - The callback to add.
     */
    add(callback: Callback) {
      callbacks.push(callback);

      return () => this.remove(callback);
    },

    remove(callback: Callback) {
      callbacks.splice(callbacks.indexOf(callback), 1);
    },

    /**
     * Sets an error handler for callback errors.
     * @param {(error: unknown, cbIndex: number, cb: Callback) => void} handler - The error handler.
     */
    setErrorHandler(
      handler: (error: unknown, cbIndex: number, cb: Callback) => void
    ) {
      handleError = handler;
    },

    /**
     * Invokes all registered callbacks with the provided arguments.
     * @param {...Params} args - The arguments to pass to the registered callbacks.
     * @returns {R[]} Array of return values from all callbacks
     */
    invoke(...args: Params): R[] {
      return callbacks.map((callback, index) => {
        if (handleError) {
          try {
            return callback(...args);
          } catch (error) {
            handleError(error, index, callback);
            return undefined as R;
          }
        }
        return callback(...args);
      });
    },

    /**
     * Clears all registered callbacks.
     */
    clear() {
      callbacks.length = 0;
    },
  };
}

export type Multicaster<
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  Params extends unknown[] = any[],
  R = void,
> = ReturnType<typeof createMulticaster<Params, R>>;
