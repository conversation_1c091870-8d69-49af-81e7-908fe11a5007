import { <PERSON><PERSON>, Object3<PERSON>, Skinned<PERSON><PERSON>, Sprite } from "three";

import { raycastService } from "@nilo/experiment-behaviors/services/RaycastService";
import { ThreejsContainersSystem } from "@nilo/experiment-behaviors/systems/ThreejsContainersSystem";
import { ThreejsSceneSystem } from "@nilo/experiment-behaviors/systems/ThreejsSceneSystem";
import { createMulticaster } from "@nilo/experiment-behaviors/utils/createMulticaster";

import type { EcsSystemContext } from "@nilo/experiment-behaviors/system-context/createSystemContext";
import type { EcsSystem } from "@nilo/experiment-behaviors/types/EcsSystem";
import type { WorldEntityUUID } from "@nilo/experiment-behaviors/types/NilusEntityData";

export type PointerInteractionsSystemClickEvent = {
  object: Object3D;
  entityUuid: WorldEntityUUID;
};

export class PointerInteractionsSystem implements EcsSystem {
  public readonly events = {
    onClick: createMulticaster<[event: PointerInteractionsSystemClickE<PERSON>]>(),
  };

  private readonly onDispose = createMulticaster<[context: EcsSystemContext]>();

  initialize(context: EcsSystemContext) {
    const sceneSystemApi = //
      context.getRequiredSystemApiByConstructor(ThreejsSceneSystem);
    const domElement = sceneSystemApi.getDomElement();

    const onClick = (event: MouseEvent) => {
      const sceneSystemApi = //
        context.getRequiredSystemApiByConstructor(ThreejsSceneSystem);
      const worldObjectsSystemApi = //
        context.getRequiredSystemApiByConstructor(ThreejsContainersSystem);

      const camera = sceneSystemApi.getCamera();

      //// TODO: Instantiate this once, and only update on entity added/removed and relevant component changes
      //// TODO: And/or, we should filter by view cone + distance/visibility
      const candidateObjects = [] as Object3D[];
      for (const [
        uuid,
        obj,
      ] of worldObjectsSystemApi.iterateAllEntityObjectsByUuid()) {
        obj.userData.entityUuid = uuid;
        candidateObjects.push(...findClickableChildren(obj));
      }

      const intersections = raycastService.getIntersections(
        camera,
        candidateObjects,
        event,
        domElement
      );

      if (intersections.length > 0) {
        const object = findFirstClickableInList(intersections);
        if (!object) return;

        const entityUuid = findEntityUuidFromAncestors(object);
        if (!entityUuid) {
          return;
        }

        this.events.onClick.invoke({ object, entityUuid });
      }
    };

    domElement.addEventListener("pointerdown", onClick);
    this.onDispose.add(() => {
      domElement.removeEventListener("pointerdown", onClick);
    });
  }

  dispose(context: EcsSystemContext) {
    this.events.onClick.clear();
    this.onDispose.invoke(context);
  }
}

const findFirstClickableInList = (objects: Object3D[]) => {
  if (objects.length === 0) return null;
  if (objects.length === 1) return objects[0];

  const firstObject = objects[0];
  if (firstObject instanceof SkinnedMesh) {
    console.warn(
      "🎯 Skipping skinned mesh for now, as it causes problems with raycasting",
      firstObject.userData.name
    );

    return objects[1];
  }

  return firstObject;
};

const isClickableType = (obj: Object3D) => {
  if (obj instanceof Mesh) return true;
  if (obj instanceof Sprite) return true;
  return false;
};

const findClickableChildren = (obj: Object3D): Object3D[] => {
  if (obj.userData.ignorePointerEvents) return [];

  const meshes: Object3D[] = [];

  if (isClickableType(obj)) {
    meshes.push(obj);
  }

  for (const child of obj.children) {
    meshes.push(...findClickableChildren(child));
  }

  return meshes;
};

const findEntityUuidFromAncestors = (obj: Object3D): WorldEntityUUID | null => {
  let current = obj as Object3D | null;
  while (current) {
    if (current.userData.entityUuid) return current.userData.entityUuid;
    current = current.parent;
  }
  return null;
};
