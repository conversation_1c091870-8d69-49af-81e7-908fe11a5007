import {
  BuiltInComponentData,
  BuiltInComponentType,
} from "./BuiltInComponentData";
import { EntityError } from "./EntityError";
import { WorldEntityUUID, WorldEntityData } from "./NilusEntityData";

export type EntitiesDataController = {
  ///// READ
  getAllEntities: () => WorldEntityData[];
  getEntityData: (entityUuid: WorldEntityUUID) => WorldEntityData;
  getEntityComponentsDataOfType: <K extends BuiltInComponentType>(
    entityUuid: WorldEntityUUID,
    componentType: K
  ) => BuiltInComponentData<K>[];
  // getFirstEntityComponentDataOfType: <K extends BuiltInComponentType>(
  //   entityUuid: WorldEntityUUID,
  //   componentType: K
  // ) => BuiltInComponentData<K> | null;

  ///// MUTATE
  getEntityDataMutationProxy: (entityUuid: WorldEntityUUID) => WorldEntityData;
};

export type ErrorsController = {
  addError: (error: EntityError) => void;
  clearErrors: (entityUuid: WorldEntityUUID) => void;
};
