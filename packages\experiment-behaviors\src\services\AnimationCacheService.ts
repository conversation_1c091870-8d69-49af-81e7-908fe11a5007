import { AnimationClip, Object3D } from "three";

type AnimationCacheEntry = {
  sourceUri: string;
  sourceUuid: string;
  animation: AnimationClip;
};

function createAnimationCacheService() {
  const animationCache: AnimationCacheEntry[] = [];

  function cacheAnimations(
    uri: string,
    modelObject: Object3D,
    animations: AnimationClip[]
  ) {
    if (animations.length > 0) {
      console.debug(
        `🎭 Found ${animations.length} animations in model`,
        animations.map((a) => a.name)
      );

      for (const animation of animations) {
        animationCache.push({
          sourceUri: uri,
          sourceUuid: modelObject.uuid,
          animation,
        });
      }
    }
  }

  function getAnimationsForUri(uri: string): AnimationClip[] {
    return animationCache
      .filter((entry) => entry.sourceUri === uri)
      .map((entry) => entry.animation);
  }

  function getAnimationsForUuid(uuid: string): AnimationClip[] {
    return animationCache
      .filter((entry) => entry.sourceUuid === uuid)
      .map((entry) => entry.animation);
  }

  return {
    cacheAnimations,
    getAnimationsForUri,
    getAnimationsForUuid,
  };
}

export type AnimationCacheService = ReturnType<
  typeof createAnimationCacheService
>;
export const animationCacheService = createAnimationCacheService();
