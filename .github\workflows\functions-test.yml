name: Test serverless functions

on:
  workflow_dispatch: {}
  push:
    branches:
      - main
    paths:
      - .github/workflows/functions-test.yml
      - serverless/functions/**
  pull_request:
    paths:
      - .github/workflows/functions-test.yml
      - serverless/functions/**

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v4
        with:
          version: 10.14.0
      - uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: pnpm
      - run: pnpm install
      - run: pnpm --filter @nilo/firebase-schema vendor
      - run: pnpm --filter functions test
