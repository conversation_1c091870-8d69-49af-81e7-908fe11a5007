import {
  BufferGeometry,
  Group,
  Line,
  LineBasicMaterial,
  Object3D,
  Vector3,
} from "three";

import { ThreejsContainersSystem } from "@nilo/experiment-behaviors/systems/ThreejsContainersSystem";

import type { EcsSystemContext } from "@nilo/experiment-behaviors/system-context/createSystemContext";
import type { EcsSystem } from "@nilo/experiment-behaviors/types/EcsSystem";
import type { WorldEntityUUID } from "@nilo/experiment-behaviors/types/NilusEntityData";

const SIZE = 0.125;
const CHILD_KEY = "debug-draw-thingy";

export class DebugDrawSystem implements EcsSystem {
  private debugObjects = new Map<WorldEntityUUID, Object3D>();

  initialize(context: EcsSystemContext) {
    context.events.onEntityAdded.add((context, entityData) => {
      const debugObj = createDebugObject();
      debugObj.userData.name = "debug: " + entityData.name;
      debugObj.userData.ignorePointerEvents = true;

      const containersSystemApi = context.getRequiredSystemApiByConstructor(
        ThreejsContainersSystem
      );
      containersSystemApi.addThreejsChildToEntity(
        entityData.uuid,
        debugObj,
        CHILD_KEY
      );

      this.debugObjects.set(entityData.uuid, debugObj);
    });

    context.events.onEntityRemoved.add((_context, entityData) => {
      this.debugObjects.delete(entityData.uuid);

      //// It's okay to not unparent the child on entity removal here,
      //// threejsContainersSystem will automatically do that.
    });
  }

  dispose() {
    this.debugObjects.clear();
  }
}

function createDebugObject() {
  const group = new Group();

  // X axis (red)
  const xGeometry = new BufferGeometry();
  const xPoints = [new Vector3(-SIZE, 0, 0), new Vector3(SIZE, 0, 0)];
  xGeometry.setFromPoints(xPoints);
  const xLine = new Line(xGeometry, new LineBasicMaterial({ color: 0xff0000 }));
  group.add(xLine);

  // Y axis (green)
  const yGeometry = new BufferGeometry();
  const yPoints = [new Vector3(0, -SIZE, 0), new Vector3(0, SIZE, 0)];
  yGeometry.setFromPoints(yPoints);
  const yLine = new Line(yGeometry, new LineBasicMaterial({ color: 0x00ff00 }));
  group.add(yLine);

  // Z axis (blue)
  const zGeometry = new BufferGeometry();
  const zPoints = [new Vector3(0, 0, -SIZE), new Vector3(0, 0, SIZE)];
  zGeometry.setFromPoints(zPoints);
  const zLine = new Line(zGeometry, new LineBasicMaterial({ color: 0x0000ff }));
  group.add(zLine);

  return group;
}
