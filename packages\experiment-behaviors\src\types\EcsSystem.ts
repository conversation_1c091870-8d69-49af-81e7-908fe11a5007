import type { EcsSystemContext } from "@nilo/experiment-behaviors/system-context/createSystemContext";
import type { Multicaster } from "@nilo/experiment-behaviors/utils/createMulticaster";

type EcsSystemApiBase = {
  events?: Record<string, Multicaster>;
  [key: string]: unknown;
};

export interface EcsSystem {
  events?: Record<string, Multicaster>;

  initialize?: (context: EcsSystemContext) => void;

  dispose?: (context: EcsSystemContext) => void;

  getApi?: (context: EcsSystemContext) => EcsSystemApiBase;
}

export type EcsSystemApi<T extends EcsSystem> =
  NonNullable<T["getApi"]> extends (context: EcsSystemContext) => infer R
    ? R
    : never;
