import { Perspective<PERSON><PERSON>ra, Scene, WebGLRenderer } from "three";
import { OrbitControls } from "three/examples/jsm/Addons";

import { createMulticaster } from "../utils/createMulticaster";
import { createSampleCameraAndControls } from "@nilo/experiment-behaviors/factories/createSampleCameraAndControls";
import { setupSampleStageSetup } from "@nilo/experiment-behaviors/factories/setupSampleStageSetup";

import type { EcsSystemContext } from "@nilo/experiment-behaviors/system-context/createSystemContext";
import type { EcsSystem } from "@nilo/experiment-behaviors/types/EcsSystem";

export class ThreejsSceneSystem implements EcsSystem {
  private readonly scene: Scene;
  private readonly camera: PerspectiveCamera;
  private readonly renderer: WebGLRenderer;
  private readonly controls: OrbitControls;
  private readonly domElement: HTMLCanvasElement;

  private resolutionFactor = 1;

  private readonly onDispose = createMulticaster();

  constructor() {
    // Scene
    this.scene = new Scene();
    this.onDispose.add(() => {
      this.scene.clear();
    });

    // Renderer
    this.renderer = new WebGLRenderer({ antialias: true });
    this.domElement = this.renderer.domElement;
    this.domElement.style.width = "100%";
    this.domElement.style.height = "100%";
    this.updateRendererSize();
    this.renderer.setClearColor(0x1a1a1a);
    this.onDispose.add(() => {
      this.renderer.dispose();
    });

    // Setup camera and controls
    const { camera, controls } = createSampleCameraAndControls(this.domElement);
    this.camera = camera;
    this.controls = controls;
  }

  initialize() {
    //// Setup scene with default lighting and grid
    //// TODO: At some point we'll want to remove this,
    //// and let outsiders control how to set up the stage.
    setupSampleStageSetup(this.scene);

    // Resize handler
    const resizeHandler = () => {
      this.camera.aspect = window.innerWidth / window.innerHeight;
      this.camera.updateProjectionMatrix();
      this.updateRendererSize();
      console.debug("📏 SceneSystem: Resized renderer and camera");
    };
    window.addEventListener("resize", resizeHandler);

    this.onDispose.add(() => {
      window.removeEventListener("resize", resizeHandler);
    });
  }

  private updateRendererSize() {
    const displayWidth = window.innerWidth;
    const displayHeight = window.innerHeight;
    const renderWidth = Math.floor(displayWidth * this.resolutionFactor);
    const renderHeight = Math.floor(displayHeight * this.resolutionFactor);

    this.renderer.setSize(renderWidth, renderHeight, false);
    this.domElement.style.width = `${displayWidth}px`;
    this.domElement.style.height = `${displayHeight}px`;
  }

  setResolutionFactor(factor: number) {
    this.resolutionFactor = factor;
    this.updateRendererSize();
  }

  getDomElement(): HTMLCanvasElement {
    return this.domElement;
  }

  getCamera(): PerspectiveCamera {
    return this.camera;
  }

  getRenderer() {
    return this.renderer;
  }

  getControls() {
    return this.controls;
  }

  getScene() {
    return this.scene;
  }

  onFrame() {
    this.controls.update();
    // Rendering is handled externally
  }

  dispose() {
    this.onDispose.invoke();
  }

  public getApi(_context: EcsSystemContext) {
    return {
      getScene: this.getScene.bind(this),
      getCamera: this.getCamera.bind(this),
      getDomElement: this.getDomElement.bind(this),
      getRenderer: this.getRenderer.bind(this),
      getCameraControls: this.getControls.bind(this),
    };
  }
}
