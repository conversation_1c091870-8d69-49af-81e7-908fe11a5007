type OverrideId = string;
type OverrideSource = string;

interface PropertyOverride<T> {
  id: OverrideId;
  value: T;
  source: OverrideSource;
}

type SomeObject = object;

export class PropertyOverrideService {
  private originalValues = new WeakMap<SomeObject, Map<string, unknown>>();
  private overrides = new WeakMap<
    SomeObject,
    Map<string, PropertyOverride<unknown>[]>
  >();
  private nextOverrideId = 0;

  /**
   * Adds an override for a property on an object
   * @returns An ID that can be used to remove this specific override
   */
  addPropertyOverride<T extends SomeObject, K extends keyof T & string>(
    object: T,
    propertyName: K,
    value: T[K],
    source: OverrideSource
  ): OverrideId {
    // Initialize maps if they don't exist
    if (!this.originalValues.has(object)) {
      this.originalValues.set(object, new Map());
      this.overrides.set(object, new Map());
    }

    const originalValues = this.originalValues.get(object)!;
    const overrides = this.overrides.get(object)!;

    // Store original value if this is the first override
    if (!originalValues.has(propertyName)) {
      originalValues.set(propertyName, object[propertyName]);
    }

    // Initialize override array if it doesn't exist
    if (!overrides.has(propertyName)) {
      overrides.set(propertyName, []);
    }

    const overrideId = `override_${this.nextOverrideId++}`;
    const overrideArray = overrides.get(propertyName)!;

    overrideArray.push({ id: overrideId, value, source });

    // Apply the most recent override
    this.applyLatestOverride(object, propertyName);

    return overrideId;
  }

  /**
   * Checks if an object has any overrides from a specific source
   */
  hasOverrideFromSource(
    object: SomeObject,
    propertyName: string,
    source: OverrideSource
  ): boolean {
    const overrides = this.overrides.get(object);
    if (!overrides) return false;

    const overrideArray = overrides.get(propertyName);
    if (!overrideArray) return false;

    return overrideArray.some((o) => o.source === source);
  }

  /**
   * Gets all override sources for a property on an object
   */
  getOverrideSources(
    object: SomeObject,
    propertyName: string
  ): Set<OverrideSource> {
    const overrides = this.overrides.get(object);
    if (!overrides) return new Set();

    const overrideArray = overrides.get(propertyName);
    if (!overrideArray) return new Set();

    return new Set(overrideArray.map((o) => o.source));
  }

  /**
   * Removes a specific override by ID
   * @returns true if the override was found and removed
   */
  removePropertyOverride<T extends SomeObject, K extends keyof T & string>(
    object: T,
    propertyName: K,
    overrideId: OverrideId
  ): boolean {
    const overrides = this.overrides.get(object);
    if (!overrides) return false;

    const overrideArray = overrides.get(propertyName);
    if (!overrideArray) return false;

    const initialLength = overrideArray.length;
    const filtered = overrideArray.filter((o) => o.id !== overrideId);

    if (filtered.length === initialLength) return false;

    overrides.set(propertyName, filtered);
    this.applyLatestOverride(object, propertyName);
    return true;
  }

  /**
   * Removes all overrides for a specific property
   */
  removeAllPropertyOverrides<T extends SomeObject, K extends keyof T & string>(
    object: T,
    propertyName: K
  ): void {
    const overrides = this.overrides.get(object);
    if (!overrides) return;

    overrides.delete(propertyName);
    this.restoreOriginalValue(object, propertyName);
  }

  /**
   * Removes all overrides for all properties on an object
   */
  removeAllOverrides<T extends SomeObject>(object: T): void {
    const overrides = this.overrides.get(object);
    if (!overrides) return;

    for (const propertyName of overrides.keys()) {
      this.restoreOriginalValue(object, propertyName as keyof T & string);
    }

    this.overrides.delete(object);
    this.originalValues.delete(object);
  }

  private applyLatestOverride<T extends SomeObject, K extends keyof T & string>(
    object: T,
    propertyName: K
  ): void {
    const overrides = this.overrides.get(object);
    if (!overrides) return;

    const overrideArray = overrides.get(propertyName);
    if (!overrideArray || overrideArray.length === 0) {
      this.restoreOriginalValue(object, propertyName);
      return;
    }

    const latestOverride = overrideArray[overrideArray.length - 1];
    object[propertyName] = latestOverride.value as T[K];
  }

  private restoreOriginalValue<
    T extends SomeObject,
    K extends keyof T & string,
  >(object: T, propertyName: K): void {
    const originalValues = this.originalValues.get(object);
    if (!originalValues) return;

    const originalValue = originalValues.get(propertyName);
    if (originalValue !== undefined) {
      object[propertyName] = originalValue as T[K];
    }
  }

  /**
   * Cleanup all overrides and original values for an object
   */
  dispose(object: SomeObject): void {
    this.removeAllOverrides(object);
  }
}
