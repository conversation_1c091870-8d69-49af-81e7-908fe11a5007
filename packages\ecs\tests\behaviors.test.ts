import { expect, test } from "@jest/globals";
import { World } from "../src/world";
import { NameBehavior } from "../src/behavior";
import { Name } from "../src/component";
import { Prefab, saveEntityToPrefab } from "../src/prefab";
import { EntityBuilder } from "../src/entityData";
import {
  PhysicalObjectBehavior,
  Position,
  PositionBehavior,
  Velocity,
  VelocityBehavior,
} from "./common";

test("behaviors", () => {
  const world = new World();
  const builder = new EntityBuilder();

  const nameBehavior = new NameBehavior("test");
  nameBehavior.addToEntity(builder);
  const entity = builder.spawn(world);

  expect(entity.getComponent(Name)).toBe("test");
});

test("prefab", () => {
  const world = new World();
  const prefab = new Prefab("test", [new NameBehavior("test")]);
  prefab.addBehavior(new VelocityBehavior({ x: 0, y: 1 }));
  expect(prefab.hasBehavior(VelocityBehavior)).toBe(true);
  expect(prefab.hasBehavior(PositionBehavior)).toBe(false);

  prefab.addMissingDependencies();

  expect(prefab.hasBehavior(PositionBehavior)).toBe(true);

  prefab.spawnPrefab(world);
});

test("prefab with subdependencies", () => {
  const world = new World();
  const prefab = new Prefab("test", [
    new PhysicalObjectBehavior(),
    new NameBehavior("test"),
  ]);

  prefab.addMissingDependencies();

  expect(prefab.hasBehavior(PositionBehavior)).toBe(true);
  expect(prefab.hasBehavior(VelocityBehavior)).toBe(true);

  const entity = prefab.spawnPrefab(world);

  expect(entity.getComponent(Position)).toBeDefined();
  expect(entity.getComponent(Velocity)).toBeDefined();
  expect(entity.getComponent(Name)).toBe("test");
});

test("prefab saving", () => {
  const world = new World();
  const prefab = new Prefab("test", [new NameBehavior("test")]);
  prefab.addBehavior(new VelocityBehavior({ x: 0, y: 1 }));
  prefab.addMissingDependencies();

  const entity = prefab.spawnPrefab(world);

  entity.setComponent(Position, { x: 1, y: 2 });

  const savedPrefab = saveEntityToPrefab(entity);
  expect(savedPrefab).toBeDefined();
  expect(savedPrefab!.hasBehavior(PositionBehavior)).toBe(true);
  expect(savedPrefab!.hasBehavior(VelocityBehavior)).toBe(true);
  expect(savedPrefab!.getBehavior(PositionBehavior)?.position).toEqual({
    x: 1,
    y: 2,
  });

  // original prefab should not have been modified
  expect(prefab.getBehavior(PositionBehavior)?.position).toEqual({
    x: 0,
    y: 0,
  });

  expect(savedPrefab!.getBehavior(VelocityBehavior)?.velocity).toEqual({
    x: 0,
    y: 1,
  });

  const entity2 = savedPrefab!.spawnPrefab(world);
  expect(entity2.getComponent(Position)).toEqual({ x: 1, y: 2 });
  expect(entity2.getComponent(Velocity)).toEqual({ x: 0, y: 1 });
  expect(entity2.getComponent(Name)).toEqual("test");
});
