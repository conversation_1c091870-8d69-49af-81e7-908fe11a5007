import { type EcsSystemContext } from "../system-context/createSystemContext";

type CleanupFunction = () => void;

type SystemEventCallback<Id extends string, Rest extends unknown[]> = (
  context: EcsSystemContext,
  id: Id,
  ...rest: Rest
) => void | CleanupFunction;

type CleanupMap<Id extends string> = Map<Id, CleanupFunction[]>;

/**
 * Creates a specialized multicaster for system events that manages cleanup functions.
 *
 * This multicaster enforces a specific parameter structure:
 * - First parameter is always the system context (EcsSystemContext)
 * - Second parameter is always an identifier (extends string)
 * - Additional parameters can be specified via the Rest generic
 *
 * Callbacks can return either void or a cleanup function. When a callback returns
 * a cleanup function, it is stored and can be executed later using invokeAndClearCleanups.
 *
 * @example
 * ```ts
 * // Create a multicaster for entity lifecycle events
 * const onEntityAdded = createSystemEventMulticaster<'entityId', [position: Vector3, radius: number]>();
 *
 * // Add a callback that creates and manages a Three.js sphere
 * onEntityAdded.add((context, id, position, radius) => {
 *   // Create sphere geometry and material
 *   const geometry = new SphereGeometry(radius);
 *   const material = new MeshStandardMaterial({ color: 0x00ff00 });
 *   const sphere = new Mesh(geometry, material);
 *
 *   // Position and add to scene
 *   sphere.position.copy(position);
 *   context.scene.add(sphere);
 *
 *   // Return cleanup function to remove and dispose resources
 *   return () => {
 *     context.scene.remove(sphere);
 *     geometry.dispose();
 *     material.dispose();
 *   };
 * });
 *
 * // When an entity is added to the system
 * onEntityAdded.invoke(systemContext, 'sphere-123', new Vector3(1, 2, 3), 0.5);
 *
 * // Later, when the entity is removed
 * onEntityAdded.invokeAndClearCleanups('sphere-123');
 * ```
 *
 * @typeParam Id - The type of identifier used to track entities/events (must extend string)
 * @typeParam Rest - Additional parameters that will be passed to callbacks after context and id
 * @returns A multicaster instance with specialized system event handling
 */
export function createSystemEventMulticaster<
  Id extends string,
  Rest extends unknown[] = [],
>() {
  type Callback = SystemEventCallback<Id, Rest>;
  const callbacks: Callback[] = [];
  const cleanupMap: CleanupMap<Id> = new Map();

  let handleError:
    | ((error: unknown, cbIndex: number, cb: Callback) => void)
    | undefined;

  return {
    add(callback: Callback) {
      callbacks.push(callback);
      return () => this.remove(callback);
    },

    remove(callback: Callback) {
      callbacks.splice(callbacks.indexOf(callback), 1);
    },

    setErrorHandler(
      handler: (error: unknown, cbIndex: number, cb: Callback) => void
    ) {
      handleError = handler;
    },

    invoke(context: EcsSystemContext, id: Id, ...rest: Rest) {
      callbacks.forEach((callback, index) => {
        if (handleError) {
          try {
            const result = callback(context, id, ...rest);
            if (typeof result === "function") {
              const cleanups = cleanupMap.get(id) ?? [];
              cleanups.push(result);
              cleanupMap.set(id, cleanups);
            }
          } catch (error) {
            handleError(error, index, callback);
          }
        } else {
          const result = callback(context, id, ...rest);
          if (typeof result === "function") {
            const cleanups = cleanupMap.get(id) ?? [];
            cleanups.push(result);
            cleanupMap.set(id, cleanups);
          }
        }
      });
    },

    invokeAndClearCleanups(id: Id) {
      const cleanups = cleanupMap.get(id) ?? [];
      cleanups.forEach((cleanup) => cleanup());
      cleanupMap.delete(id);
    },

    clear() {
      callbacks.length = 0;
      cleanupMap.clear();
    },
  };
}

export type SystemEventMulticaster<
  Id extends string,
  Rest extends unknown[] = [],
> = ReturnType<typeof createSystemEventMulticaster<Id, Rest>>;
