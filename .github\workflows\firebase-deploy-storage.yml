name: Deploy Firebase Storage
on:
  workflow_dispatch: {}
  push:
    branches:
      - main
    paths:
      - .github/workflows/firebase-deploy-storage.yml
      - storage.rules
  pull_request:
    types:
      - opened
      - reopened
      - synchronize

concurrency:
  group: firebase-storage-${{ github.head_ref || github.ref_name }}
  cancel-in-progress: true

jobs:
  create_bucket_and_deploy_rules:
    runs-on: ubuntu-latest
    permissions:
      contents: "read"
      id-token: "write"
    steps:
      - uses: actions/checkout@v4
      - id: auth
        uses: google-github-actions/auth@v2
        with:
          # Note: these are not secrets
          service_account: "<EMAIL>"
          workload_identity_provider: "projects/************/locations/global/workloadIdentityPools/github/providers/nilo-technologies-org"
          token_format: access_token
      - uses: pnpm/action-setup@v4
        with:
          version: 10.14.0
      - uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: pnpm
      - run: pnpm install
      - name: Get isolated environment component names
        id: isolated-env
        run: |
          pnpm exec isolated-environments emitGithubActionVars "${{ github.head_ref || github.ref_name }}" >> $GITHUB_OUTPUT
      # only run this step for pull requests
      - if: github.event_name == 'pull_request'
        uses: google-github-actions/setup-gcloud@v2
        with:
          project_id: "nilo-technologies"
      # only run this step for pull requests
      - if: github.event_name == 'pull_request'
        name: Create bucket
        run: |
          if gcloud storage buckets describe gs://${{ steps.isolated-env.outputs.bucket }} &> /dev/null; then
            echo "Bucket already exists"
          else
            echo "Creating bucket"
            gcloud storage buckets create gs://${{ steps.isolated-env.outputs.bucket }} \
              --project=nilo-technologies \
              --location=eu
            gsutil cors set serverless/storage/cors.json gs://${{ steps.isolated-env.outputs.bucket }}
            echo "Adding Firebase support to bucket"
            curl -X POST \
              -H "Authorization: Bearer ${{ steps.auth.outputs.access_token }}" \
              -H "Content-Type: application/json" \
              "https://firebasestorage.googleapis.com/v1beta/projects/nilo-technologies/buckets/${{ steps.isolated-env.outputs.bucket }}:addFirebase" \
              --data-binary "{}"
          fi
      - name: Update firebase.json
        run: |
          jq \
            --arg database "${{ steps.isolated-env.outputs.database }}" \
            --arg target "${{ steps.isolated-env.outputs.name }}" \
            --arg bucket "${{ steps.isolated-env.outputs.bucket }}" \
            '.firestore[0].database = $database | .storage[0].target = $target | .storage[0].bucket = $bucket' \
            firebase.json > firebase.json.new
          mv firebase.json.new firebase.json
          cat firebase.json
      - name: Deploy storage rules
        run: |
          pnpm exec firebase target:apply storage ${{ steps.isolated-env.outputs.name }} ${{ steps.isolated-env.outputs.bucket }}
          pnpm exec firebase deploy --non-interactive --force --only storage:${{ steps.isolated-env.outputs.name }} || (sleep 30 && pnpm exec firebase deploy --non-interactive --force --only storage:${{ steps.isolated-env.outputs.name }})
