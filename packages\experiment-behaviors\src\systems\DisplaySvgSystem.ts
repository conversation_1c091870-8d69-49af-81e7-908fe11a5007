import {
  Canvas<PERSON><PERSON><PERSON>,
  DoubleSide,
  Mesh,
  MeshBasicMaterial,
  Object3D,
  PlaneGeometry,
  Sprite,
  SpriteMaterial,
} from "three";

import {
  createTemplateCompiler,
  TemplateCompiler,
} from "../utils/used-by-systems/createTemplateCompiler";
import { WorldEntityComponentUUID } from "../types/NilusComponentData";
import { CustomAttributesSystem } from "@nilo/experiment-behaviors/systems/CustomAttributesSystem";
import { ThreejsContainersSystem } from "@nilo/experiment-behaviors/systems/ThreejsContainersSystem";
import { BuiltInComponentTypes } from "@nilo/experiment-behaviors/types/BuiltInComponentData";
import { createMulticaster } from "@nilo/experiment-behaviors/utils/createMulticaster";
import { renderSvgToTexture } from "@nilo/experiment-behaviors/utils/used-by-systems/renderSvgToTexture";

import type { EcsSystemContext } from "@nilo/experiment-behaviors/system-context/createSystemContext";
import type { BuiltInComponentData } from "@nilo/experiment-behaviors/types/BuiltInComponentData";
import type {
  EcsSystem,
  EcsSystemApi,
} from "@nilo/experiment-behaviors/types/EcsSystem";
import type { WorldEntityUUID } from "@nilo/experiment-behaviors/types/NilusEntityData";

const CHILD_KEY_PREFIX = "svg-plane";

export class DisplaySvgSystem implements EcsSystem {
  private readonly templateCompiler = createTemplateCompiler();
  private readonly svgControllers = new Map<
    WorldEntityComponentUUID,
    SvgController
  >();
  private readonly onDispose = createMulticaster();

  initialize(context: EcsSystemContext) {
    const { events } = context;

    events.onEntityComponentAddedByType.add(
      BuiltInComponentTypes.svgPlane,
      async (context, entityData, componentData) => {
        const containersApi = //
          context.getRequiredSystemApiByConstructor(ThreejsContainersSystem);

        const ctrl = new SvgController(
          entityData.uuid,
          componentData,
          this.templateCompiler,
          containersApi,
          (error) => context.handleError(error, "Failed to create SVG display")
        );

        this.svgControllers.set(componentData.uuid, ctrl);

        ctrl.onComponentParamsChange(componentData.params, context);
      }
    );

    events.onEntityComponentRemovedByType.add(
      BuiltInComponentTypes.svgPlane,
      (_context, _entityData, componentData) => {
        const ctrl = this.svgControllers.get(componentData.uuid);
        if (ctrl) {
          ctrl.cleanup();
          this.svgControllers.delete(componentData.uuid);
        }
      }
    );

    events.onEntityComponentUpdatedByType.add(
      BuiltInComponentTypes.svgPlane,
      (context, _entityData, componentData) => {
        const ctrl = this.svgControllers.get(componentData.uuid);
        if (!ctrl) {
          console.warn(
            "🔶 No SVG controller found for component:",
            componentData.uuid
          );
          return;
        }

        ctrl.onComponentParamsChange(componentData.params, context);
      }
    );

    const customAttributesApi = //
      context.getRequiredSystemApiByConstructor(CustomAttributesSystem);
    customAttributesApi.events.onAttributeChanged.add(({ entityUuid }) => {
      // Find all controllers for this entity and update them
      for (const ctrl of this.svgControllers.values()) {
        if (ctrl.entityUuid === entityUuid) {
          ctrl.onComponentParamsChange(ctrl.componentData.params, context);
        }
      }
    });
  }

  dispose() {
    for (const ctrl of this.svgControllers.values()) {
      ctrl.cleanup();
    }
    this.templateCompiler.dispose();
    this.svgControllers.clear();
    this.onDispose.invoke();
  }
}

function createMesh(texture: CanvasTexture, offsetY: number): Mesh {
  const material = new MeshBasicMaterial({
    map: texture,
    transparent: true,
    side: DoubleSide,
  });

  const geometry = new PlaneGeometry(1, 1);
  const mesh = new Mesh(geometry, material);
  mesh.position.y = offsetY;
  return mesh;
}

function createSprite(texture: CanvasTexture, offsetY: number): Sprite {
  const material = new SpriteMaterial({
    map: texture,
    transparent: true,
  });

  const sprite = new Sprite(material);
  sprite.position.y = offsetY;
  return sprite;
}

class SvgController {
  private currentDisplay?: Object3D;
  private currentDisplayHtmlCode?: string;

  private readonly childKey: string;

  constructor(
    public readonly entityUuid: WorldEntityUUID,
    public readonly componentData: BuiltInComponentData<
      typeof BuiltInComponentTypes.svgPlane
    >,
    private readonly templateCompiler: TemplateCompiler,
    private readonly containersApi: EcsSystemApi<ThreejsContainersSystem>,
    private readonly onError: (error: unknown) => void
  ) {
    this.childKey = `${CHILD_KEY_PREFIX}:${this.componentData.uuid}`;
  }

  async createDisplay(htmlCode: string) {
    //// Skip recreation if the HTML code hasn't changed
    //// (e.g. maybe an attribute we don't care about changed)
    if (this.currentDisplayHtmlCode === htmlCode) {
      return;
    }

    //// Clean up previous display if it exists
    //// TODO: Ideally we'd either avoid recreating the display if
    //// we can only update a texture,
    //// OR implement obj3d pooling, (which essentially achieve the same thing)
    this.cleanup();

    try {
      const texture = await renderSvgToTexture(htmlCode);
      this.currentDisplay = this.componentData.params.billboard
        ? createSprite(texture, this.componentData.params.offsetY)
        : createMesh(texture, this.componentData.params.offsetY);

      this.containersApi.addThreejsChildToEntity(
        this.entityUuid,
        this.currentDisplay,
        this.childKey
      );

      //// Store the HTML code for future comparison
      this.currentDisplayHtmlCode = htmlCode;
    } catch (error) {
      this.onError(error);
    }
  }

  onComponentParamsChange(
    _updates: Partial<
      BuiltInComponentData<typeof BuiltInComponentTypes.svgPlane>["params"]
    >,
    systemContext: EcsSystemContext
  ) {
    const customAttributesApi = //
      systemContext.getRequiredSystemApiByConstructor(CustomAttributesSystem);
    const currentAttributes = customAttributesApi.getAttributes(
      this.entityUuid
    );
    const svgCodeContext = { attributes: currentAttributes };
    const processedHtmlCode = this.resolveCodeSnippets(
      this.componentData.params.htmlCode,
      svgCodeContext
    );
    this.createDisplay(processedHtmlCode);
  }

  private resolveCodeSnippets(
    text: string,
    context: Record<string, unknown>
  ): string {
    return text.replace(/\{\{([^}]+)\}\}/g, (match, code) => {
      try {
        const fn = this.templateCompiler.compile(code.trim());
        const result = fn(context);
        return String(result ?? match);
      } catch (error) {
        console.warn("🔶 Template evaluation error:", error, "for code:", code);
        return "[ERR]";
      }
    });
  }

  cleanup() {
    if (this.currentDisplay) {
      this.containersApi.removeThreejsChildFromEntity(
        this.entityUuid,
        this.childKey
      );
      if (this.currentDisplay instanceof Mesh) {
        this.currentDisplay.geometry.dispose();
        (this.currentDisplay.material as MeshBasicMaterial).dispose();
      } else if (this.currentDisplay instanceof Sprite) {
        (this.currentDisplay.material as SpriteMaterial).dispose();
      }
      this.currentDisplay = undefined;
      this.currentDisplayHtmlCode = undefined;
    }
  }
}
