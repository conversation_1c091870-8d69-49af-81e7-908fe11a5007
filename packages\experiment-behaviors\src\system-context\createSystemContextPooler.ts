import type {
  Entities<PERSON><PERSON><PERSON><PERSON>roller,
  ErrorsController,
} from "../types/EcsEngineControllers";
import type { EcsSystem } from "../types/EcsSystem";
import type { WorldEntityComponentUUID } from "../types/NilusComponentData";
import type { WorldEntityUUID } from "../types/NilusEntityData";
import { EcsEngineEventsWithoutInvoke } from "../createEcsEngineEvents";
import {
  createEcsSystemContext,
  type EcsSystemContext,
} from "./createSystemContext";

type SystemContextScope = {
  entityUuid?: WorldEntityUUID;
  componentUuid?: WorldEntityComponentUUID;
  systemName?: string;
  during?: string;
};

type PooledContext = {
  systemContext: EcsSystemContext;
  returnSystemContextToPool: () => void;
};

export function createSystemContextPooler(
  systems: EcsSystem[],
  entitiesDataController: EntitiesDataController,
  errorsController: <PERSON>rro<PERSON><PERSON><PERSON>roller,
  events: EcsEngineEventsWithoutInvoke
) {
  const availableContexts: EcsSystemContext[] = [];
  const activeContexts = new Set<EcsSystemContext>();

  const createNewContext = (scope: SystemContextScope) =>
    createEcsSystemContext(
      systems,
      scope,
      entitiesDataController,
      errorsController,
      events
    );

  const resetContextScope = (context: EcsSystemContext) => {
    // Reset all scope-related properties to undefined
    Object.assign(context, {
      entityUuid: undefined,
      componentUuid: undefined,
      systemName: undefined,
      during: undefined,
    });
  };

  ///////

  const getContext = (scope: SystemContextScope): PooledContext => {
    let context: EcsSystemContext;

    if (availableContexts.length > 0) {
      context = availableContexts.pop()!;
      // Update scope properties
      Object.assign(context, scope);
    } else {
      context = createNewContext(scope);
    }

    activeContexts.add(context);

    return {
      systemContext: context,
      returnSystemContextToPool: () => returnContextToPool(context),
    };
  };

  const returnContextToPool = (context: EcsSystemContext) => {
    if (!activeContexts.has(context)) {
      console.warn("🔄 Attempted to return context that wasn't active");
      return;
    }

    resetContextScope(context);
    activeContexts.delete(context);
    availableContexts.push(context);
  };

  return {
    getContext,

    // For debugging/monitoring
    getPoolStats() {
      return {
        available: availableContexts.length,
        active: activeContexts.size,
        total: availableContexts.length + activeContexts.size,
      };
    },
  };
}

export type SystemContextPooler = ReturnType<typeof createSystemContextPooler>;
