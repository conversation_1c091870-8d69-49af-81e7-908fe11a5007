import type {
  BuiltInComponentType,
  BuiltInComponentData,
} from "../../types/BuiltInComponentData";
import type { WorldEntityData } from "../../types/NilusEntityData";

export function findFirstEntityComponentDataOfType<
  K extends BuiltInComponentType,
>(
  entityData: WorldEntityData | null | undefined,
  componentType: K
): BuiltInComponentData<K> | null {
  if (!entityData?.components) return null;

  const component = Object.values(entityData.components).find(
    (c) => c.type === componentType
  );
  if (!component) return null;

  // Type assertion is safe here because we know the component type matches
  return component as BuiltInComponentData<K>;
}
