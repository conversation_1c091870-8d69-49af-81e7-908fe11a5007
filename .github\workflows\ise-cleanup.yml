name: Cleanup ISE (Firebase Functions, Firestore Database, Storage Bucket and Server Fleet) on PR close

on:
  pull_request:
    types:
      - closed

jobs:
  cleanup-firebase:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      id-token: write
    steps:
      - uses: actions/checkout@v4
      - uses: google-github-actions/auth@v2
        with:
          # Note: these are not secrets
          service_account: "<EMAIL>"
          workload_identity_provider: "projects/************/locations/global/workloadIdentityPools/github/providers/nilo-technologies-org"
      - uses: pnpm/action-setup@v4
        with:
          version: 10.14.0
      - uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: pnpm
      - run: pnpm install
      - name: Get isolated environment component names
        id: isolated-env
        run: |
          pnpm exec isolated-environments emitGithubActionVars "${{ github.head_ref || github.ref_name }}" | tee $GITHUB_OUTPUT
      - run: pnpm exec firebase --non-interactive functions:delete --force ${{ steps.isolated-env.outputs.functions }}
        continue-on-error: true
      - name: Delete database
        run: |
          if pnpm exec firebase firestore:databases:get ${{ steps.isolated-env.outputs.database }} &> /dev/null; then
            echo "Database exists, deleting"
            pnpm exec firebase --non-interactive firestore:databases:delete --force ${{ steps.isolated-env.outputs.database }}
          else
            echo "Database does not exist, skipping"
          fi
      - name: Delete bucket
        run: |
          if gcloud storage buckets describe gs://${{ steps.isolated-env.outputs.bucket }} &> /dev/null; then
            echo "Bucket exists, deleting"
            gcloud storage rm -r --quiet gs://${{ steps.isolated-env.outputs.bucket }}
          else
            echo "Bucket does not exist, skipping"
          fi

  cleanup-server-fleet:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      id-token: write
    steps:
      - uses: actions/checkout@v4
      - uses: google-github-actions/auth@v2
        with:
          # Note: these are not secrets
          service_account: "<EMAIL>"
          workload_identity_provider: "projects/************/locations/global/workloadIdentityPools/github/providers/nilo-technologies-org"
          token_format: access_token
          access_token_lifetime: 300s
      - uses: google-github-actions/get-gke-credentials@v1
        with:
          cluster_name: nilo-eu-west4-cluster
          location: europe-west4
      - uses: pnpm/action-setup@v4
        with:
          version: 10.14.0
      - uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: pnpm
      - run: pnpm install
      - name: Get isolated environment component names
        id: isolated-env
        run: |
          pnpm exec isolated-environments emitGithubActionVars "${{ github.head_ref || github.ref_name }}" | tee $GITHUB_OUTPUT
      - run: helm delete ${{ steps.isolated-env.outputs.fleet }} --ignore-not-found
