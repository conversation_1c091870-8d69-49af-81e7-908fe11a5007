import { EcsEngine, EcsSystemConstructor } from "../createEcsEngine";
import { EcsSystem } from "../types/EcsSystem";

export function wrapEngineWithSystems<
  TEngine extends EcsEngine,
  TSystems extends Record<string, EcsSystemConstructor<EcsSystem>>,
>(engine: TEngine, systemConstructors: TSystems) {
  const systems = Object.entries(systemConstructors).reduce(
    (acc, [key, constructor]) => {
      acc[key] = engine.addSystem(constructor);
      return acc;
    },
    {} as Record<string, EcsSystem>
  );

  return Object.assign(engine, { systems }) as TEngine & {
    systems: { [K in keyof TSystems]: InstanceType<TSystems[K]> };
  };
}
