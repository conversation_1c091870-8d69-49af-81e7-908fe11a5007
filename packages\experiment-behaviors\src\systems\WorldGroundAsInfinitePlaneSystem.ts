import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>e,
  <PERSON>Blending,
  Float32<PERSON>uffer<PERSON><PERSON>ri<PERSON>e,
  <PERSON><PERSON>,
  MeshStandardMaterial,
  OneFactor,
  OneMinusSrcAlphaFactor,
  PlaneGeometry,
  SRGBColorSpace,
} from "three";

import { ThreejsContainersSystem } from "@nilo/experiment-behaviors/systems/ThreejsContainersSystem";
import { ThreejsSceneSystem } from "@nilo/experiment-behaviors/systems/ThreejsSceneSystem";
import { textureLoadingService } from "@nilo/experiment-behaviors/services/TextureLoadingService";
import { BuiltInComponentTypes } from "@nilo/experiment-behaviors/types/BuiltInComponentData";
import { createMulticaster } from "@nilo/experiment-behaviors/utils/createMulticaster";

import type { EcsSystemContext } from "@nilo/experiment-behaviors/system-context/createSystemContext";
import type { BuiltInComponentData } from "@nilo/experiment-behaviors/types/BuiltInComponentData";
import type {
  EcsSystem,
  EcsSystem<PERSON><PERSON>,
} from "@nilo/experiment-behaviors/types/EcsSystem";
import type { WorldEntityUUID } from "@nilo/experiment-behaviors/types/NilusEntityData";

const CHILD_KEY = "world-ground-as-infinite-plane";

export class WorldGroundAsInfinitePlaneSystem implements EcsSystem {
  private readonly groundControllers = new Map<
    WorldEntityUUID,
    GroundPlaneController
  >();
  private readonly onDispose = createMulticaster();

  initialize({ events }: EcsSystemContext) {
    events.onEntityComponentAddedByType.add(
      BuiltInComponentTypes.worldGroundAsInfinitePlane,
      (context, entityData, componentData) => {
        const containersApi = //
          context.getRequiredSystemApiByConstructor(ThreejsContainersSystem);
        const sceneApi =
          context.getRequiredSystemApiByConstructor(ThreejsSceneSystem);

        const ctrl = new GroundPlaneController(
          entityData.uuid,
          componentData,
          containersApi,
          sceneApi
        );

        this.groundControllers.set(entityData.uuid, ctrl);
        console.debug("🌍 Added ground plane for entity:", entityData.uuid);
      }
    );

    events.onEntityComponentRemovedByType.add(
      BuiltInComponentTypes.worldGroundAsInfinitePlane,
      (_context, entityData) => {
        const ctrl = this.groundControllers.get(entityData.uuid);
        if (ctrl) {
          ctrl.dispose();
          this.groundControllers.delete(entityData.uuid);
          console.debug("🌍 Removed ground plane for entity:", entityData.uuid);
        }
      }
    );

    events.onEntityComponentUpdatedByType.add(
      BuiltInComponentTypes.worldGroundAsInfinitePlane,
      (_context, entityData, componentData) => {
        const ctrl = this.groundControllers.get(entityData.uuid);
        if (!ctrl) {
          console.warn(
            "🔶 Entity has no ground plane controller, skipping:",
            entityData.uuid
          );
          return;
        }

        ctrl.onComponentParamsChange(componentData.params);
      }
    );

    events.onFrame.add((_context, _delta) => {
      for (const ctrl of this.groundControllers.values()) {
        ctrl.onEnterFrame();
      }
    });
  }

  dispose() {
    for (const ctrl of this.groundControllers.values()) {
      ctrl.dispose();
    }
    this.groundControllers.clear();
    this.onDispose.invoke();
    console.debug("🧹 WorldGroundAsInfinitePlaneSystem: Disposed");
  }
}

class GroundPlaneController {
  private readonly mesh: Mesh;
  private readonly gridSize = 256;
  private readonly gridScale = 1;

  constructor(
    private readonly entityUuid: WorldEntityUUID,
    public readonly componentData: BuiltInComponentData<
      typeof BuiltInComponentTypes.worldGroundAsInfinitePlane
    >,
    private readonly containersApi: EcsSystemApi<ThreejsContainersSystem>,
    private readonly sceneApi: EcsSystemApi<ThreejsSceneSystem>
  ) {
    this.mesh = this.createGroundPlane(componentData.params.textureUri);
    this.containersApi.addThreejsChildToEntity(
      entityUuid,
      this.mesh,
      CHILD_KEY
    );
  }

  private createGroundPlane(textureUri: string): Mesh {
    const texture = textureLoadingService.getOrLoadTexture(textureUri, {
      repeat: true,
    });
    texture.repeat.set(
      this.gridSize * this.gridScale,
      this.gridSize * this.gridScale
    );

    const alphaTexture = textureLoadingService.getOrLoadTexture(
      "/textures/alpha.png"
    );
    alphaTexture.colorSpace = SRGBColorSpace;

    const geometry = new PlaneGeometry(
      this.gridSize,
      this.gridSize,
      this.gridSize,
      this.gridSize
    );
    geometry.rotateX(-Math.PI / 2);

    const uvAttribute = geometry.getAttribute("uv") as BufferAttribute;
    const uv2 = new Float32BufferAttribute(
      new Float32Array(uvAttribute.count * 2),
      2
    ).copy(uvAttribute);
    geometry.setAttribute("uv2", uv2);

    const material = new MeshStandardMaterial({
      map: texture,
      alphaMap: alphaTexture,
      blending: CustomBlending,
      blendSrcAlpha: OneFactor,
      blendDstAlpha: OneMinusSrcAlphaFactor,
      premultipliedAlpha: false,
      polygonOffset: true,
      polygonOffsetFactor: 1,
      polygonOffsetUnits: 1,
    });

    const mesh = new Mesh(geometry, material);
    mesh.receiveShadow = true;
    mesh.name = "GroundPlaneMesh";
    mesh.renderOrder = 1;

    return mesh;
  }

  onEnterFrame() {
    const camera = this.sceneApi.getCamera();

    // Update mesh position to follow camera
    this.mesh.position.set(camera.position.x, 0, camera.position.z);

    // Update texture offset
    const material = this.mesh.material as MeshStandardMaterial;
    if (material.map) {
      material.map.offset.set(
        (camera.position.x * this.gridScale) % this.gridSize,
        -(camera.position.z * this.gridScale) % this.gridSize
      );
    }
  }

  onComponentParamsChange(
    updates: Partial<
      BuiltInComponentData<
        typeof BuiltInComponentTypes.worldGroundAsInfinitePlane
      >["params"]
    >
  ) {
    if (updates.textureUri) {
      const texture = textureLoadingService.getOrLoadTexture(
        updates.textureUri,
        { repeat: true }
      );
      texture.repeat.set(
        this.gridSize * this.gridScale,
        this.gridSize * this.gridScale
      );
      (this.mesh.material as MeshStandardMaterial).map = texture;
    }
  }

  dispose() {
    this.containersApi.removeThreejsChildFromEntity(this.entityUuid, CHILD_KEY);
    this.mesh.geometry.dispose();
    (this.mesh.material as MeshStandardMaterial).dispose();
  }
}
