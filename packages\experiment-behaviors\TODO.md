# To Do (ECS)

## Engine

- [ ] TASKS tracker (for loading and such)
- [ ] Actually construct per-event context for systems

- [ ] Refactor: events with obj instead of param array (easy to destructure for only what you need)
- [ ] Refactor: Get rid of .getApi().events; just .getApi().onSomething.add() is enough
- [ ] Refactor: Add `.activateComponentEffect()`, or something like that, which returns a cleanup function, to systems.
  - Or maybe engine/context should have a similar group of multicasters to .events, but only for events that
    can be paired with complementary opposite events, like entity added/removed, component added/removed, etc.
    Then each invokation for `add` gathers returned cleanup callbacks and invokes them on `remove`.
    E.g. `.onComponentActivate(handler)` where handler adds a child object to the entity's container,
    but also returns a function that removes it. Then the engine broadcasts `onComponentActivated`, saves the
    clean up callbacks and calls them when deactivating the component (e.g. because the entity is removed, or
    just a param changed and component needs restarting)
- [ ] Refactor: Don't do stuff in systems' constructors - use an .activateEffect() method instead, with cleanup.
      Then we can have .isActive and toggle at runtime.

- [x] Move ECS to packages/nilo-ecs
- [x] De-couple ECS engine from stores (world & errors)
  - [x] Get read-only entities/components' data
  - [x] Get dedicated service for mutating entities/components' data
- [x] Bundle get/change entity data methods into a single interface passed to the engine?
- [x] Remove context from systems' constructors
- [x] Systems should not be able to access other systems' instance refs directly.
      Replace context.getSystem() (etc) with context.getSystemApi()
      which returns new public method on the system interface - .getApi(),
      and move any public methods and stuff there.
- [x] Refactor: Maybe we can pass not instances of systems, but their classes only. Let engine call new X(). Pros/cons?
  - [-] If we give a list systems to engine constr. we can make it generic.
    Only allow .getSystem() to return one of these as type.
  - [x] Better yet, use a dict.
        Then `new Engine({ models: ModelFromUriSystem })` can return `Engine & { systems: { models: ModelFromUriSystem } }`.
        No need to check then getsystem() for nullish returns — use engine.models, assume since it was given on constr, it's there.
- [x] ⚠ Strictly differentiate between updates to transform properties and component props
      (don't update in the same func, sep subs/invokations)
  - `engine.addEntity()` / `engine.removeEntity()`
  - `engine.addEntityComponent()` / `engine.removeEntityComponent()`
  - `engine.updateEntityComponentProp()`
  - `engine.updateEntityTransform()`
  - and similarly for components
- [x] Add various multicasters to systems so that other systems can hook up to that
- [x] Pool the system context objects, so that we don't create them all the time. (we only need to replace the scope stuff I think)
- [x] Systems should react to component changes only on components they care about...
- [x] pass around the context properly to systems, instead of them keeping it as a member
- [-] Refactor: Perhaps .onEntityAdded() can return a cleanup function, to be pooled by the engine and invoked when removed

### Mugs

- [ ] Fix: Auto-import not working since moving ECS to packages/ecs
- [ ] Fix: Bunch of systems assuming an entity has only one of x type of component
- [x] Fix: Material override component not cleaning up, like, at all
- [x] Fix: ModelFromUri system seamingly not properly cleaning up too
- [x] Fix: Performance tanking when CustomBehaviors are running.

## More cool systems/comp types/services

- [ ] Errors system - simply handles and broadcasts errors. GUI can track this and render its own thing based on this.
  - Engine has onError multicaster and does nothing by default, then error system subs and saves the errors?
  - Actually, is a system even necessary then? Outsider can just listen for and track their own errors. Clear them onEntity/ComponentRefresh or something.
- [ ] Polish/motion system - adds nice tweens and effects whenever an entity is added/removed/etc. Like elastic scale up fomr 0 (pop-in) on added; polygons explosion on removed; etc.
- [x] Particle system and comp types
- [ ] Better particle system and comp types (not using Points)
- [ ] Sprite/Billboard system and comp types
- [ ] Jolt physics system and comp types
- [ ] Audio system and comp types
- [ ] Pointer interactions via BVH
- [ ] Pointer interactions via GPU-picking
- [ ] GUI-helper systems?
  - [ ] Outline system - renders an outline around given entities, potentially useful for hover/selection/highlighting.
  - [ ] 3D-to-screen-space system that can report objects' 3D origin position to canvas point, maybe also bounding box
        center, and overal bounds rect. Useful to ask and render gui elements in the right place easily.
  - [ ] Maybe also a simple 3D gui helper system which maintains empty containers for every entity, ready for you to
        attach 3D ui elements to on-demand without worrying about positioning etc. Must support local and world transform,
        or maybe more granularly - follow entity rotation & follow entity scale options. Example use case: gizmos.
