import { Camera, Intersection, Object3D, Raycaster, Vector2 } from "three";

//// Array to reuse
const intersects = [] as Intersection[];

function createRaycastService() {
  const raycaster = new Raycaster();
  const mouse = new Vector2();

  function updateMousePosition(event: MouseEvent, domElement: HTMLElement) {
    const rect = domElement.getBoundingClientRect();
    mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
    mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;
  }

  function getIntersections(
    camera: Camera,
    objects: Object3D[],
    event: MouseEvent,
    domElement: HTMLElement
  ) {
    updateMousePosition(event, domElement);

    raycaster.setFromCamera(mouse, camera);

    intersects.length = 0;
    raycaster.intersectObjects(objects, false, intersects);

    return intersects.map((intersect) => {
      const object = intersect.object;
      return object;
    });
  }

  return {
    getIntersections,
  };
}

export type RaycastService = ReturnType<typeof createRaycastService>;
export const raycastService = createRaycastService();
