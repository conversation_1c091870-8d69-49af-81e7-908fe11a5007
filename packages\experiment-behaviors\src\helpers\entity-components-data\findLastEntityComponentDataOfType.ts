import type {
  BuiltInComponentType,
  BuiltInComponentData,
} from "../../types/BuiltInComponentData";
import type { WorldEntityData } from "../../types/NilusEntityData";

export function findLastEntityComponentDataOfType<
  K extends BuiltInComponentType,
>(
  entityData: WorldEntityData | null | undefined,
  componentType: K
): BuiltInComponentData<K> | null {
  if (!entityData?.components) return null;

  const components = Object.values(entityData.components);
  // Find last matching component by iterating in reverse
  for (let i = components.length - 1; i >= 0; i--) {
    const component = components[i];
    if (component.type === componentType) {
      // Type assertion is safe here because we know the component type matches
      return component as BuiltInComponentData<K>;
    }
  }

  return null;
}
