import type {
  BuiltInComponentType,
  BuiltInComponentData,
} from "../../types/BuiltInComponentData";
import type { WorldEntityData } from "../../types/NilusEntityData";

export function findAllEntityComponentsDataOfType<
  K extends BuiltInComponentType,
>(
  entityData: WorldEntityData | null | undefined,
  componentType: K
): readonly BuiltInComponentData<K>[] {
  if (!entityData?.components) return [];

  return Object.values(entityData.components).filter(
    (c): c is BuiltInComponentData<K> => c.type === componentType
  );
}
