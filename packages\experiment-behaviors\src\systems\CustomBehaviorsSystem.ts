import type { EcsSystemContext } from "../system-context/createSystemContext";
import type { EcsSystem } from "../types/EcsSystem";
import type { WorldEntityUUID } from "@nilo/experiment-behaviors/types/NilusEntityData";

export class CustomBehaviorsSystem implements EcsSystem {
  private readonly entityUuids = new Set<WorldEntityUUID>();
  private readonly behaviorCompiler = createBehaviorCompiler();

  initialize({ events }: EcsSystemContext) {
    events.onEntityAdded.add((_context, entityData) => {
      this.entityUuids.add(entityData.uuid);
    });

    events.onEntityRemoved.add((_context, entityData) => {
      this.entityUuids.delete(entityData.uuid);
    });

    events.onFrame.add((context, delta) => {
      for (const entityUuid of this.entityUuids) {
        ///// By design, this is the only way to mutate entity data
        const entityDataProxy = context.getEntityDataMutationProxy(entityUuid);

        const components = context.getEntityComponentsDataOfType(
          entityUuid,
          "customBehavior"
        );

        for (const component of components) {
          const { onFrameCode } = component.params;
          if (!onFrameCode) continue;

          try {
            const behaviorFn = this.behaviorCompiler.compile(onFrameCode);
            behaviorFn.call(entityDataProxy, entityDataProxy, delta);
          } catch (error) {
            const errorMessage =
              error instanceof Error ? error.message : String(error);
            context.handleError(
              entityUuid,
              `CustomBehavior onFrame error: ${errorMessage}`
            );
          }
        }
      }
    });
  }

  dispose(): void {
    this.behaviorCompiler.dispose();
    console.debug("🧹 CustomBehaviorSystem: Disposed");
  }
}

type BehaviorFunction = (myObject: unknown, delta: number) => void;

const createBehaviorCompiler = () => {
  const cache = new Map<string, BehaviorFunction>();

  return {
    compile: (code: string) => {
      let fn = cache.get(code);
      if (!fn) {
        fn = new Function("myObject", "delta", code) as BehaviorFunction;
        cache.set(code, fn);
      }
      return fn;
    },
    dispose: () => {
      cache.clear();
    },
  };
};
