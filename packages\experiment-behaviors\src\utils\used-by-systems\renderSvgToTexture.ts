import { CanvasTexture } from "three";

// Cache for rendered SVG textures
const textureCache = new Map<string, CanvasTexture>();

export function renderSvgToTexture(htmlCode: string): Promise<CanvasTexture> {
  if (textureCache.has(htmlCode)) {
    return Promise.resolve(textureCache.get(htmlCode)!);
  }

  return new Promise((resolve, reject) => {
    try {
      // Create a canvas element
      const canvas = document.createElement("canvas");
      canvas.width = 512; // Fixed size for now
      canvas.height = 512;
      const ctx = canvas.getContext("2d");
      if (!ctx) {
        reject(new Error("🔴 Failed to get canvas context"));
        return;
      }

      // Create a temporary div to render the SVG
      const tempDiv = document.createElement("div");
      tempDiv.innerHTML = htmlCode;
      document.body.appendChild(tempDiv);

      // Get the SVG element and ensure it's properly parsed
      const svgElement = tempDiv.querySelector("svg");
      if (!svgElement) {
        document.body.removeChild(tempDiv);
        reject(new Error("🔴 No SVG element found in HTML code"));
        return;
      }

      // Set SVG dimensions
      svgElement.setAttribute("width", "512");
      svgElement.setAttribute("height", "512");

      // Convert SVG to data URL
      const svgData = new XMLSerializer().serializeToString(svgElement);
      const svgBlob = new Blob([svgData], {
        type: "image/svg+xml;charset=utf-8",
      });
      const url = URL.createObjectURL(svgBlob);

      const img = new Image();

      img.onload = () => {
        try {
          // Clear canvas and draw the image
          ctx.clearRect(0, 0, canvas.width, canvas.height);
          ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

          // Create texture from canvas
          const texture = new CanvasTexture(canvas);
          texture.needsUpdate = true;

          // Clean up
          URL.revokeObjectURL(url);
          document.body.removeChild(tempDiv);
          textureCache.set(htmlCode, texture);
          resolve(texture);
        } catch (e) {
          URL.revokeObjectURL(url);
          document.body.removeChild(tempDiv);
          reject(new Error(`🔴 Failed to create texture: ${e}`));
        }
      };

      img.onerror = (e) => {
        console.error("🔴 Failed to load SVG image", e);
        URL.revokeObjectURL(url);
        document.body.removeChild(tempDiv);
        reject(new Error("🔴 Failed to load SVG image"));
      };

      img.src = url;
    } catch (e) {
      console.error("🔴 Error in renderSvgToTexture:", e);
      reject(new Error(`🔴 Error in renderSvgToTexture: ${e}`));
    }
  });
}
