import type { WorldEntityComponentUUID } from "./NilusComponentData";
import type { WorldEntityUUID } from "./NilusEntityData";

export interface EntityError {
  ////////////////////////////
  //// Error
  ////

  /**
   * **What** failed. Short descriptive text.
   */
  message: string;

  /**
   * **Why** it failed. Direct reference to the original error object.
   */
  sourceError?: Error;

  /**
   * Consider the error handled by the engine?
   *
   * Example **handled** (good) errors:
   * - Bad uri for model, so it couldn't be loaded and rendered. The model is not rendered, so it's not a problem.
   * - Custom behaviour script throws an error. We display it, not a problem.
   *
   * Example **unhandled** (bad) errors:
   * - Any error thrown by our systems or services, that was cought on engine level.
   */
  handled: boolean;

  ////////////////////////////
  //// Scope
  ////

  entityUuid: WorldEntityUUID;
  componentUuid?: WorldEntityComponentUUID;
  systemName?: string;
  during?: string;
}
