export function createKeyedMulticaster<
  Key extends string | number | symbol,
  <PERSON><PERSON> extends unknown[],
  R = void,
>() {
  type Callback = (...args: Params) => R;
  type Listeners = Map<Key, Callback[]>;

  const listeners: Listeners = new Map();

  let handleError:
    | ((error: unknown, key: Key, cbIndex: number, cb: Callback) => void)
    | undefined;

  return {
    /**
     * Adds a callback to be invoked when the multicaster is triggered for a specific key.
     * @param {Key} key - The key to group the callback under
     * @param {Callback} callback - The callback to add
     */
    add(key: Key, callback: Callback) {
      const keyListeners = listeners.get(key) ?? [];
      keyListeners.push(callback);
      listeners.set(key, keyListeners);

      return () => this.remove(key, callback);
    },

    /**
     * Removes a specific callback for a key
     */
    remove(key: Key, callback: Callback) {
      const keyListeners = listeners.get(key);
      if (!keyListeners) return;

      const index = keyListeners.indexOf(callback);
      if (index === -1) return;

      keyListeners.splice(index, 1);
      if (keyListeners.length === 0) {
        listeners.delete(key);
      }
    },

    /**
     * Sets an error handler for callback errors
     */
    setErrorHandler(
      handler: (error: unknown, key: Key, cbIndex: number, cb: Callback) => void
    ) {
      handleError = handler;
    },

    /**
     * Invokes all registered callbacks for a specific key with the provided arguments
     */
    invoke(key: Key, ...args: Params): R[] {
      const keyListeners = listeners.get(key);
      if (!keyListeners) return [];

      return keyListeners.map((callback, index) => {
        if (handleError) {
          try {
            return callback(...args);
          } catch (error) {
            handleError(error, key, index, callback);
            return undefined as R;
          }
        }
        return callback(...args);
      });
    },

    /**
     * Clears all listeners for a specific key
     */
    clearKey(key: Key) {
      listeners.delete(key);
    },

    /**
     * Clears all registered callbacks for all keys
     */
    clear() {
      listeners.clear();
    },
  };
}

export type KeyedMulticaster<
  Key extends string | number | symbol,
  Params extends unknown[] = unknown[],
  R = void,
> = ReturnType<typeof createKeyedMulticaster<Key, Params, R>>;
