import { Group, Object3D } from "three";

import type { EcsSystemContext } from "@nilo/experiment-behaviors/system-context/createSystemContext";
import type { EcsSystem } from "@nilo/experiment-behaviors/types/EcsSystem";
import type {
  WorldEntityData,
  WorldEntityTransformData,
  WorldEntityUUID,
} from "@nilo/experiment-behaviors/types/NilusEntityData";

export class ThreejsContainersSystem implements EcsSystem {
  private readonly parent = new Group();
  private readonly entityContainers = new Map<WorldEntityUUID, Object3D>();
  private readonly childrenPerEntityPerSystem =
    createObjectsPerEntityPerSystemMap();

  initialize(context: EcsSystemContext) {
    context.events.onEntityAdded.add((_context, entityData) => {
      this.onEntityAdded(entityData);
    });
    context.events.onEntityRemoved.add((_context, entityData) => {
      this.onEntityRemoved(entityData);
    });
    context.events.onEntityTransformUpdated.add((_context, entityData) => {
      this.onEntityTransformUpdated(entityData);
    });
  }

  public getApi(_context: EcsSystemContext) {
    const requireEntityContainerObject = (uuid: WorldEntityUUID) => {
      const container = this.getEntityObject(uuid);
      if (!container) {
        throw new Error(`Threejs container not found for entity ${uuid}`);
      }
      return container;
    };

    return {
      addThreejsChildToEntity: (
        entityUuid: WorldEntityUUID,
        child: Object3D,
        key: string
      ) => {
        const container = requireEntityContainerObject(entityUuid);

        // const key = `${key}:${uuid}` as const;
        const existingChild = this.childrenPerEntityPerSystem.get(
          entityUuid,
          key
        );

        if (existingChild) {
          if (existingChild === child) {
            console.debug(`🟡 Child already in container, skipping`, key);
            return;
          }

          existingChild.parent?.remove(existingChild);
          this.childrenPerEntityPerSystem.delete(entityUuid, key);
          console.debug(`🔴 Removed existing child with key ${key}`);
        }

        child.userData.entityUuid = entityUuid;
        child.userData.name = child.userData.name ?? "child:" + key;
        child.name = child.name ?? child.userData.name;

        container.add(child);
        this.childrenPerEntityPerSystem.set(entityUuid, key, child);
        console.debug(`🟢 Added child with key ${key}`);
      },
      removeThreejsChildFromEntity: (uuid: WorldEntityUUID, key: string) => {
        const existingChild = this.childrenPerEntityPerSystem.get(uuid, key);
        if (existingChild) {
          existingChild.parent?.remove(existingChild);
          this.childrenPerEntityPerSystem.delete(uuid, key);
          console.debug(`🔴 Removed child with key ${key}`);
        }
      },
      clearThreejsChildrenForEntity: (uuid: WorldEntityUUID) => {
        const children = this.childrenPerEntityPerSystem.getAllForEntity(uuid);
        if (children) {
          for (const child of children) {
            child.parent?.remove(child);
          }
        }
        this.childrenPerEntityPerSystem.clear(uuid);
      },
      iterateAllEntityObjectsByUuid: () => {
        return this.iterateAllEntityObjectsByUuid();
      },
    };
  }

  private updateEntityTransform(entity: WorldEntityData): void {
    const obj = this.entityContainers.get(entity.uuid);
    if (!obj) return;

    updateEntityTransform(obj, entity.transform);
  }

  getParentContainer(): Group {
    return this.parent;
  }

  getEntityObject(uuid: WorldEntityUUID): Object3D | undefined {
    return this.entityContainers.get(uuid);
  }

  iterateAllEntityObjectsByUuid(): Iterable<[WorldEntityUUID, Object3D]> {
    return this.entityContainers.entries();
  }

  onEntityAdded(entity: WorldEntityData): void {
    const entityObj = new Object3D();
    entityObj.userData.name = "container:" + entity.name;
    entityObj.userData.entityUuid = entity.uuid;

    this.entityContainers.set(entity.uuid, entityObj);
    this.parent.add(entityObj);
    this.updateEntityTransform(entity);

    console.debug(`➕ Added Object3D for entity ${entity.uuid}`);
  }

  onEntityRemoved(entity: WorldEntityData): void {
    const obj = this.entityContainers.get(entity.uuid);
    if (obj) {
      this.parent.remove(obj);
      this.entityContainers.delete(entity.uuid);
      console.debug(`➖ Removed Object3D for entity ${entity.uuid}`);
    }

    this.childrenPerEntityPerSystem.clear(entity.uuid);
  }

  onEntityTransformUpdated(_entity: WorldEntityData): void {
    this.updateEntityTransform(_entity);
  }

  dispose(): void {
    this.parent.clear();
    this.entityContainers.clear();
    console.debug("🧹 ThreejsContainersSystem: Disposed");
  }
}

const createObjectsPerEntityPerSystemMap = () => {
  const map = new Map<WorldEntityUUID, Map<string, Object3D>>();

  return {
    get: (uuid: WorldEntityUUID, key: string) => {
      const entityChildren = map.get(uuid);
      return entityChildren?.get(key) ?? null;
    },
    getAllForEntity: (uuid: WorldEntityUUID) => {
      return map.get(uuid)?.values() ?? null;
    },
    set: (uuid: WorldEntityUUID, key: string, child: Object3D) => {
      const entityChildren = map.get(uuid);
      if (!entityChildren) {
        const children = new Map<string, Object3D>();
        map.set(uuid, children);
      } else {
        entityChildren.set(key, child);
      }
    },
    delete: (uuid: WorldEntityUUID, key: string) => {
      const entityChildren = map.get(uuid);
      if (entityChildren) {
        entityChildren.delete(key);
      }
      if (entityChildren?.size === 0) {
        map.delete(uuid);
      }
    },
    clear: (uuid: WorldEntityUUID) => {
      map.delete(uuid);
    },
  };
};

function updateEntityTransform(
  obj: Object3D,
  transform: WorldEntityTransformData
) {
  const [x, y, z] = transform.position;
  obj.position.set(x, y, z);
  const [rx, ry, rz] = transform.rotation;
  obj.rotation.set(rx, ry, rz);
  const [sx, sy, sz] = transform.scale;
  obj.scale.set(sx, sy, sz);
}
