# Entity Component System (ECS)

A lightweight, type-safe Entity Component System implementation designed for high-performance and high degree of decoupling.

## Architecture

**Data-Oriented Design**: Separates data (components) from behavior (systems) for better performance and maintainability.

**Archetype Storage**: Entities with identical component sets are grouped together for efficient memory layout and cache performance.

## Core Concepts

**Entities**: Unique identifiers representing entities or objects in the world.

**Components**: Associated data defining entity properties and state.

**Systems**: Logic that operates on entities with specific component combinations and modifies the components data on the entities.

**Queries**: Type-safe iteration over entities matching component patterns.

**Behaviors**: Reusable logic patterns that can be attached to entities, providing modular functionality and composition.

**Prefabs**: Entity templates defining component sets and initial data for rapid entity creation and consistency.

**Sparse Components**: Components stored separately from dense archetype storage, optimized for infrequently used or specialized data.

**Change Detection**: Automatic component dirty tracking allows queries to visit only modified entities.

**Services**: Shared singleton storage for the world, used for dependency injection.

## Systems

Systems encapsulate game logic and operate on entities with specific component combinations. They are registered with the world and automatically receive notifications about entity changes.

Systems are ideal for:

- Physics and movement calculations
- AI behavior updates
- Rendering state management
- Game state synchronization
- Any logic that needs to operate on multiple entities with specific component combinations

## Services

The world supports a service container for dependency injection:

```typescript
class GameService {
  // Service implementation
}

// Register service
world.addService(new GameService());

// Access service
const gameService = world.service(GameService);
```

## Performance Considerations

- The system uses an archetype-based storage model for efficient component access
- Queries are cached and only refreshed when the archetype composition changes
- Component data is stored in contiguous arrays for better cache utilization
- Entity operations (add/remove components) trigger archetype changes only when necessary

## Type Safety

The system is designed with TypeScript to provide compile-time type checking for:

- Component definitions and their data structures
- Query patterns and result types
- Entity operations and component access
