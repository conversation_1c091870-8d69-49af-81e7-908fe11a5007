import { createMulticaster } from "./utils/createMulticaster";
import { createKeyedMulticaster } from "./utils/createKeyedMulticaster";

import type { EcsSystemContext } from "./system-context/createSystemContext";
import type { WorldEntityData } from "./types/NilusEntityData";
import type { WorldEntityComponentData } from "./types/NilusComponentData";
import type {
  BuiltInComponentData,
  BuiltInComponentType,
} from "./types/BuiltInComponentData";

export function createEcsEngineEvents(): EcsEngineEvents {
  return {
    // TODO: Fix this

    // @ts-ignore
    onEntityAdded: createMulticaster(),
    // @ts-ignore
    onEntityRemoved: createMulticaster(),
    // @ts-ignore
    onEntityTransformUpdated: createMulticaster(),

    // @ts-ignore
    onEntityComponentAdded: createMulticaster(),
    // @ts-ignore
    onEntityComponentRemoved: createMulticaster(),
    // @ts-ignore
    onEntityComponentUpdated: createMulticaster(),

    // TODO: Fix this

    // @ts-ignore
    onEntityComponentAddedByType: createKeyedMulticaster(),
    // @ts-ignore
    onEntityComponentRemovedByType: createKeyedMulticaster(),
    // @ts-ignore
    onEntityComponentUpdatedByType: createKeyedMulticaster(),

    // @ts-ignore
    onFrame: createMulticaster(),
  };
}

// function createMulticastersWithCleanup() {
//   type ListenerFunc = <T extends unknown[]>(
//     context: EcsSystemContext,
//     ...args: T
//   ) => void;

//   type CleanupFunc = () => void;

//   const onEntityAddedMulticaster = createSystemEventMulticaster<
//     WorldEntityUUID,
//     [WorldEntityData]
//   >();
// }

type CleanupFunc = () => void;

type EcsEngineEventsMulticaster<T extends unknown[]> = {
  invoke: (context: EcsSystemContext, ...args: T) => CleanupFunc;
  add: (
    listener: (context: EcsSystemContext, ...args: T) => void
  ) => CleanupFunc;
  remove: (
    listener: (context: EcsSystemContext, ...args: T) => void
  ) => CleanupFunc;
};

type EcsEngineEventsKeyedMulticaster = {
  invoke: <K extends BuiltInComponentType>(
    key: K,
    context: EcsSystemContext,
    entityData: WorldEntityData,
    componentData: BuiltInComponentData<K>
  ) => CleanupFunc;
  add: <K extends BuiltInComponentType>(
    key: K,
    listener: (
      context: EcsSystemContext,
      entityData: WorldEntityData,
      componentData: BuiltInComponentData<K>
    ) => void
  ) => CleanupFunc;
  remove: <K extends BuiltInComponentType>(
    key: K,
    listener: (
      context: EcsSystemContext,
      entityData: WorldEntityData,
      componentData: BuiltInComponentData<K>
    ) => void
  ) => CleanupFunc;
};

export type EcsEngineEvents = {
  onEntityAdded: EcsEngineEventsMulticaster<[entityData: WorldEntityData]>;
  onEntityRemoved: EcsEngineEventsMulticaster<[entityData: WorldEntityData]>;
  onEntityTransformUpdated: EcsEngineEventsMulticaster<
    [entityData: WorldEntityData]
  >;
  onEntityComponentAdded: EcsEngineEventsMulticaster<
    [entityData: WorldEntityData, componentData: WorldEntityComponentData]
  >;
  onEntityComponentRemoved: EcsEngineEventsMulticaster<
    [entityData: WorldEntityData, componentData: WorldEntityComponentData]
  >;
  onEntityComponentUpdated: EcsEngineEventsMulticaster<
    [entityData: WorldEntityData, componentData: WorldEntityComponentData]
  >;
  onEntityComponentAddedByType: EcsEngineEventsKeyedMulticaster;
  onEntityComponentRemovedByType: EcsEngineEventsKeyedMulticaster;
  onEntityComponentUpdatedByType: EcsEngineEventsKeyedMulticaster;

  onFrame: EcsEngineEventsMulticaster<[deltaTimeSeconds: number]>;
};

export type EcsEngineEventsWithoutInvoke = {
  [K in keyof EcsEngineEvents]: EcsEngineEvents[K] extends EcsEngineEventsMulticaster<
    infer T
  >
    ? Omit<EcsEngineEventsMulticaster<T>, "invoke">
    : EcsEngineEvents[K] extends EcsEngineEventsKeyedMulticaster
      ? Omit<EcsEngineEventsKeyedMulticaster, "invoke">
      : never;
};
