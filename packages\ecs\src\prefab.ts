import { Behavior, BehaviorConstructor } from "./behavior";
import { Component } from "./component";
import { EntityBuilder, EntityData } from "./entityData";
import { World } from "./world";

/** A prefab represents a descriptive template from which to create one or more entities
 *
 * Prefabs are to be considered *immutable* and do not change after creation. Entities retain a reference to their originating prefab.
 *
 * Entities can be saved to a new prefab, which will contain the same behaviors as the original prefab.
 */
export class Prefab {
  public name: string;
  private _behaviors: Behavior[];
  private _behaviorSet: Set<BehaviorConstructor>;

  public get behaviors(): Behavior[] {
    return this._behaviors;
  }

  constructor(name: string, behaviors: Behavior[]) {
    this.name = name;
    this._behaviors = behaviors;
    this._behaviorSet = new Set(
      behaviors.map((b) => b.constructor as BehaviorConstructor)
    );
  }

  /** Build the prefab */
  build(): EntityBuilder {
    if (this.hasMissingDependencies()) {
      throw new Error(
        `Prefab ${this.name} has missing dependencies. Call addMissingDependencies() first.`
      );
    }

    const builder = new EntityBuilder();
    this.addToBuilder(builder);
    return builder;
  }

  /** Spawns a new entity from the prefab */
  spawnPrefab(world: World): EntityData {
    return this.build().spawn(world);
  }

  /** Adds the prefab to an existing entity */
  addToBuilder(entity: EntityBuilder): void {
    entity.addComponent(PrefabOrigin, this);
    this._behaviors.forEach((behavior) => behavior.addToEntity(entity));
  }

  addBehavior(behavior: Behavior): Prefab {
    if (
      this._behaviorSet.has(behavior.constructor as BehaviorConstructor) &&
      !behavior.allowMultiple
    ) {
      throw new Error(`Behavior ${behavior.constructor.name} already added`);
    }

    this._behaviors.push(behavior);
    this._behaviorSet.add(behavior.constructor as BehaviorConstructor);
    return this;
  }

  hasBehavior(behavior: BehaviorConstructor): boolean {
    return this._behaviorSet.has(behavior);
  }

  getBehavior<T extends Behavior>(behavior: new () => T): T | null {
    return this._behaviors.find((b) => b.constructor === behavior) as T | null;
  }

  hasMissingDependencies(): boolean {
    return this._behaviors.some((behavior) =>
      behavior
        .dependencies?.()
        .some((dependency) => !this._behaviorSet.has(dependency))
    );
  }

  /** Add all behaviors that are not already present to the prefab */
  addMissingDependencies(): void {
    const newBehaviors: Behavior[] = [];
    const behaviorSet = new Set(this._behaviors.map((b) => b.constructor));

    // Keep checking for new dependencies until none are found
    do {
      newBehaviors.forEach((b) =>
        this._behaviorSet.add(b.constructor as BehaviorConstructor)
      );
      this._behaviors.push(...newBehaviors);
      newBehaviors.length = 0;

      // Check all behaviors (including newly added ones) for dependencies
      this._behaviors.forEach((behavior) => {
        behavior.dependencies?.().forEach((dependency) => {
          const found = behaviorSet.has(dependency);

          if (!found) {
            // Add the new dependency
            const newBehavior = new dependency();
            newBehaviors.push(newBehavior);
            behaviorSet.add(dependency);
          }
        });
      });

      // Add all new behaviors to the prefab
    } while (newBehaviors.length > 0);

    newBehaviors.forEach((b) =>
      this._behaviorSet.add(b.constructor as BehaviorConstructor)
    );
    this._behaviors.push(...newBehaviors);
  }
}

/** Stores the entity into a new prefab */
export function saveEntityToPrefab(entity: EntityData): Prefab {
  const prefab = entity.getComponent(PrefabOrigin);
  if (!prefab) {
    throw new Error(`Entity ${entity.id} is not a prefab`);
  }

  const behaviors: Behavior[] = [];
  for (const behavior of prefab.behaviors) {
    behaviors.push(behavior.getFromEntity(entity));
  }

  return new Prefab(prefab.name, behaviors);
}

export const PrefabOrigin = new Component<Prefab>("prefab").withDescription(
  "Originating prefab of the entity"
);
