import { FEATURE_FLAGS_CONFIG } from "./feature-flags-config";

// Extract the union type of all feature flag names
export type FeatureFlag = (typeof FEATURE_FLAGS_CONFIG)[number]["name"];

type ExtractOnType<
  TConfig extends readonly unknown[],
  TFlagName extends string,
> =
  Extract<TConfig[number], { name: TFlagName }> extends {
    on: infer TOn;
  }
    ? TOn
    : never;

// Automatically derive variant types from the configuration
// This ensures the types are always in sync with the config
// Type inference is based on the 'on' value which represents the enabled state
export type FeatureFlagVariants = {
  [K in FeatureFlag]: ExtractOnType<typeof FEATURE_FLAGS_CONFIG, K>;
};

// Helper type to extract the variant type for a specific flag
export type GetFeatureFlagVariantType<T extends FeatureFlag> =
  FeatureFlagVariants[T];

// Type guard to check if a string is a valid feature flag name
export function isValidFeatureFlag(flagName: string): flagName is FeatureFlag {
  return FEATURE_FLAGS_CONFIG.some((flag) => flag.name === flagName);
}

// Helper function to get the local value for a flag based on enabled state
export function getFeatureFlagLocalValue<T extends FeatureFlag>(
  flagName: T,
  forceEnabled?: boolean
): GetFeatureFlagVariantType<T> {
  const config = FEATURE_FLAGS_CONFIG.find((flag) => flag.name === flagName);
  if (!config) {
    throw new Error(`Feature flag "${flagName}" not found in configuration`);
  }

  // Use forceEnabled if provided, otherwise use config.enabled
  const isEnabled = forceEnabled !== undefined ? forceEnabled : config.enabled;

  // Return 'on' value if enabled, 'off' value if disabled
  return (isEnabled ? config.on : config.off) as GetFeatureFlagVariantType<T>;
}

// Helper function to get the default (off) value for a flag
export function getFeatureFlagDisabledValue<T extends FeatureFlag>(
  flagName: T
): GetFeatureFlagVariantType<T> {
  const config = FEATURE_FLAGS_CONFIG.find((flag) => flag.name === flagName);
  if (!config) {
    throw new Error(`Feature flag "${flagName}" not found in configuration`);
  }

  // Always return the 'off' value as the default
  return config.off as GetFeatureFlagVariantType<T>;
}

// Helper function to get the enabled (on) value for a flag
export function getFeatureFlagEnabledValue<T extends FeatureFlag>(
  flagName: T
): GetFeatureFlagVariantType<T> {
  const config = FEATURE_FLAGS_CONFIG.find((flag) => flag.name === flagName);
  if (!config) {
    throw new Error(`Feature flag "${flagName}" not found in configuration`);
  }

  // Return the 'on' value
  return config.on as GetFeatureFlagVariantType<T>;
}

// Helper function to check if a flag is enabled by default in local development
export function isFeatureFlagEnabledByDefault(flagName: FeatureFlag): boolean {
  const config = FEATURE_FLAGS_CONFIG.find((flag) => flag.name === flagName);
  if (!config) {
    throw new Error(`Feature flag "${flagName}" not found in configuration`);
  }

  return config.enabled;
}

// Type guard to validate if a value is type-compatible with the flag's on value
export function isFeatureFlagValueValid<T extends FeatureFlag>(
  flagName: T,
  flagValue: unknown
): flagValue is GetFeatureFlagVariantType<T> {
  const config = FEATURE_FLAGS_CONFIG.find((flag) => flag.name === flagName);
  if (!config) {
    return false;
  }

  // Check against both 'on' and 'off' values since both are valid
  return (
    isTypeCompatible(config.on, flagValue) ||
    isTypeCompatible(config.off, flagValue)
  );
}

// Helper function to check if two values are type-compatible
function isTypeCompatible(expected: unknown, actual: unknown): boolean {
  // Handle null/undefined
  if (expected === null || expected === undefined) {
    return actual === null || actual === undefined;
  }

  // Handle primitive types
  if (typeof expected !== "object") {
    return typeof expected === typeof actual;
  }

  // Handle arrays
  if (Array.isArray(expected)) {
    if (!Array.isArray(actual)) return false;
    // For arrays, check if all elements are type-compatible
    // If expected is empty, accept any array
    if (expected.length === 0) return true;
    // Check if actual array elements match the type of expected elements
    return actual.every((item) =>
      expected.some((expectedItem) => isTypeCompatible(expectedItem, item))
    );
  }

  // Handle objects
  if (typeof actual !== "object" || actual === null) {
    return false;
  }

  // Check if actual object has all required properties with compatible types
  for (const [key, expectedValue] of Object.entries(expected)) {
    if (!(key in (actual as Record<string, unknown>))) {
      return false; // Missing required property
    }

    const actualValue = (actual as Record<string, unknown>)[key];
    if (!isTypeCompatible(expectedValue, actualValue)) {
      return false; // Incompatible property type
    }
  }

  return true;
}
