/**
 * Declares a typed component that is attached to an entity.
 *
 * **NOTE**: Key *must* be unique as it is used to identify the component.
 * Use `makeComponentKey` to generate a key from a string.
 */
export class Component<T> {
  /**
   * A unique, universal, 32-bit integer key for the component.
   * Generated by the constructor by default.
   * Use `makeComponent<PERSON>ey` to generate a key from a string.
   */
  public readonly KEY: number;

  /**
   * Creates a new component for a data type.
   * @param name A string label for this component. Used to generate a unique key.
   */
  public constructor(name: string) {
    this.KEY = makeUniqueKey(name);
    this._meta = { name, sparse: false };
  }

  /**
   * Adds a description to this component's metadata.
   * @returns An instance of `this` for method chaining.
   */
  public withDescription(description: string): this {
    this._meta = { ...this._meta, description };
    return this;
  }

  /**
   * Marks this component as sparse.
   * @returns An instance of `this` for method chaining.
   */
  public withSparse(): this {
    this._meta = { ...this._meta, sparse: true };
    return this;
  }

  public get meta(): Meta {
    return this._meta;
  }

  public get NAME(): string {
    // TODO: should this still be an uppercase constant?
    return this._meta.name;
  }

  private _meta: Meta;
  private readonly _phantom?: T;
}

/** Data-independent metadata for `Component` instances. */
export interface Meta {
  /** A sparse component can be added to entities without moving them to a different archetype.
   *
   * A sparse component has *worse* performance characteristics during iteration than non-sparse (dense) components, due to the potential lookup and jumping.
   *
   * Sparse components are instead useful for highly ephemeral components or as a means to reduce archetype fragmentation for marker or tag components.
   */
  readonly sparse: boolean;

  readonly name: string;
  readonly description?: string;
}

/**
 * Generates a unique key for a component based on its name.
 *
 * This is used to deterministically generate a unique, universal key for a component.
 *
 * @param name - The name of the component.
 * @returns A 32-bit integer key.
 */
export function makeUniqueKey(name: string): number {
  // cryptographic hash functions are unavailable in the browser
  // all we need is determinism, so we'll use a simple, non-cryptographic hash function
  let hash = 0;
  for (let i = 0; i < name.length; i++) {
    const char = name.charCodeAt(i);
    hash = (hash << 5) - hash + char;
    hash |= 0; // convert to 32-bit signed integer
  }

  return Math.abs(hash);
}

export const Name = new Component<string>("name").withDescription(
  "name describing the entity"
);
