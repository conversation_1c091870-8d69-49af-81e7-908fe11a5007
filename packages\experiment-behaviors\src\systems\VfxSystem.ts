import {
  Mesh,
  MeshBasicMaterial,
  Object3D,
  PlaneGeometry,
  Sprite,
  SpriteMaterial,
} from "three";
import { ThreejsSceneSystem } from "./ThreejsSceneSystem";
import { textureLoadingService } from "@nilo/experiment-behaviors/services/TextureLoadingService";
import { createMulticaster } from "@nilo/experiment-behaviors/utils/createMulticaster";

import type { EcsSystemContext } from "@nilo/experiment-behaviors/system-context/createSystemContext";
import type { EcsSystem } from "@nilo/experiment-behaviors/types/EcsSystem";

type VfxConfig = {
  type: "plane";
  textureUri: string;
  duration: number;
  position?: [number, number, number];
  rotation?: [number, number, number];
  scale?: [number, number, number];
  transparent?: boolean;
  opacity?: number;
} & (
  | {
      onFrame: (progress: number, obj: Sprite) => void;
      billboard: true;
    }
  | {
      onFrame: (progress: number, obj: Mesh) => void;
      billboard?: false;
    }
);

export class VfxSystem implements EcsSystem {
  private readonly activeEffects = new Set<VfxInstance>();
  private readonly onDispose = createMulticaster();

  initialize(context: EcsSystemContext) {
    context.events.onFrame.add((_context, _delta) => {
      const currentTime = performance.now();

      // Update all effects and collect completed ones
      const completedEffects: VfxInstance[] = [];
      for (const effect of this.activeEffects) {
        if (effect.update(currentTime)) {
          completedEffects.push(effect);
        }
      }

      // Clean up completed effects
      for (const effect of completedEffects) {
        effect.dispose();
        this.activeEffects.delete(effect);
      }
    });
  }

  dispose() {
    for (const effect of this.activeEffects) {
      effect.dispose();
    }
    this.activeEffects.clear();
    this.onDispose.invoke();
  }

  public getApi(context: EcsSystemContext) {
    const sceneApi =
      context.getRequiredSystemApiByConstructor(ThreejsSceneSystem);
    const scene = sceneApi.getScene();

    return {
      spawnEffect: (config: VfxConfig) => {
        const effect = new VfxInstance(config, scene);
        this.activeEffects.add(effect);
        return () => {
          effect.dispose();
          this.activeEffects.delete(effect);
        };
      },
    };
  }
}

class VfxInstance {
  private readonly startTime: number;
  private readonly obj: Sprite | Mesh;
  private readonly config: VfxConfig;
  private readonly onFrame: (progress: number) => void;

  constructor(config: VfxConfig, scene: Object3D) {
    this.startTime = performance.now();
    this.config = config;
    [this.obj, this.onFrame] = this.createObjectAndOnFrame();

    if (config.position) {
      this.obj.position.set(...config.position);
    }
    if (config.rotation) {
      this.obj.rotation.set(...config.rotation);
    }
    if (config.scale) {
      this.obj.scale.set(...config.scale);
    }

    scene.add(this.obj);
  }

  private createObjectAndOnFrame(): [
    Sprite | Mesh,
    (progress: number) => void,
  ] {
    const texture = textureLoadingService.getOrLoadTexture(
      this.config.textureUri
    );

    if (this.config.billboard) {
      const material = new SpriteMaterial({
        map: texture,
        transparent: this.config.transparent ?? true,
        opacity: this.config.opacity ?? 1.0,
      });
      const sprite = new Sprite(material);
      const onFrame = this.config.onFrame;
      return [sprite, (progress) => onFrame(progress, sprite)];
    } else {
      const material = new MeshBasicMaterial({
        map: texture,
        transparent: this.config.transparent ?? true,
        opacity: this.config.opacity ?? 1.0,
      });
      const geometry = new PlaneGeometry(1, 1);
      const mesh = new Mesh(geometry, material);
      const onFrame = this.config.onFrame;
      return [mesh, (progress) => onFrame(progress, mesh)];
    }
  }

  update(currentTime: number): boolean {
    const elapsed = (currentTime - this.startTime) / 1000; // Convert to seconds
    const progress = Math.min(elapsed / this.config.duration, 1);

    this.onFrame(progress);

    return progress >= 1;
  }

  dispose() {
    if (this.obj instanceof Mesh) {
      this.obj.geometry.dispose();
      (this.obj.material as MeshBasicMaterial).dispose();
    } else if (this.obj instanceof Sprite) {
      (this.obj.material as SpriteMaterial).dispose();
    }
    this.obj.parent?.remove(this.obj);
  }
}
