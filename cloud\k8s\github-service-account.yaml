# This gives the github actions service account the ability to deploy to the cluster.
# Outside of this file, the service account was:
# - created with: `kubectl create serviceaccount github`
# - annotated with: `kubectl annotate serviceaccount github "iam.gke.io/gcp-service-account=<EMAIL>"`
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: github-deployments-role
  namespace: default
rules:
  - apiGroups: ["apps"]
    resources: ["deployments"]
    verbs: ["get", "list", "watch", "create", "update", "patch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: github-deployments-binding
  namespace: default
subjects:
  - kind: ServiceAccount
    name: github
    namespace: default
roleRef:
  kind: Role
  name: github-deployments-role
  apiGroup: rbac.authorization.k8s.io
