import { ThreejsContainersSystem } from "@nilo/experiment-behaviors/systems/ThreejsContainersSystem";
import { createPortalOrb } from "@nilo/experiment-behaviors/three/portal/createPortalOrb";
import { BuiltInComponentTypes } from "@nilo/experiment-behaviors/types/BuiltInComponentData";
import { createMulticaster } from "@nilo/experiment-behaviors/utils/createMulticaster";
import { textureLoadingService } from "@nilo/experiment-behaviors/services/TextureLoadingService";

import type { EcsSystemContext } from "@nilo/experiment-behaviors/system-context/createSystemContext";
import type { BuiltInComponentData } from "@nilo/experiment-behaviors/types/BuiltInComponentData";
import type { EcsSystem } from "@nilo/experiment-behaviors/types/EcsSystem";
import type { WorldEntityUUID } from "@nilo/experiment-behaviors/types/NilusEntityData";

const CHILD_KEY = "portal";

export class DisplayPortalsSystem implements EcsSystem {
  private readonly portalControllers = new Map<
    WorldEntityUUID,
    PortalController
  >();
  private readonly onDispose = createMulticaster();

  private animationFrameId?: number;

  initialize({ events }: EcsSystemContext) {
    events.onEntityComponentAddedByType.add(
      BuiltInComponentTypes.worldPortal,
      (context, entityData, componentData) => {
        const existingCtrl = this.portalControllers.get(entityData.uuid);
        if (existingCtrl) {
          console.warn(
            "🔶 Entity already has a portal, skipping:",
            entityData.uuid
          );
          return;
        }

        const portal = new PortalController(componentData);
        this.portalControllers.set(entityData.uuid, portal);

        const containersApi = //
          context.getRequiredSystemApiByConstructor(ThreejsContainersSystem);
        containersApi.addThreejsChildToEntity(
          entityData.uuid,
          portal.orb.mesh,
          CHILD_KEY
        );

        console.debug("🔶 Portal added:", entityData.uuid);
      }
    );

    events.onEntityComponentRemovedByType.add(
      BuiltInComponentTypes.worldPortal,
      (context, entityData) => {
        const existingCtrl = this.portalControllers.get(entityData.uuid);
        if (existingCtrl) {
          existingCtrl.dispose();
          const containersApi = //
            context.getRequiredSystemApiByConstructor(ThreejsContainersSystem);
          containersApi.removeThreejsChildFromEntity(
            entityData.uuid,
            CHILD_KEY
          );
          this.portalControllers.delete(entityData.uuid);
        }
      }
    );

    events.onEntityComponentUpdatedByType.add(
      BuiltInComponentTypes.worldPortal,
      (_context, entityData, componentData) => {
        const ctrl = this.portalControllers.get(entityData.uuid);
        if (!ctrl) {
          console.warn("🔶 Entity has no portal, skipping:", entityData.uuid);
          return;
        }

        ctrl.onComponentParamsChange(componentData.params);
      }
    );

    this.startAnimationLoop();
  }

  private startAnimationLoop() {
    const animate = () => {
      this.animationFrameId = requestAnimationFrame(animate);
    };

    animate();
  }

  dispose() {
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
    }

    this.portalControllers.forEach((ctrl) => ctrl.dispose());
    this.portalControllers.clear();
    this.onDispose.invoke();
  }
}

class PortalController {
  public readonly orb: ReturnType<typeof createPortalOrb>;
  public readonly componentData: BuiltInComponentData<
    typeof BuiltInComponentTypes.worldPortal
  >;

  constructor(
    componentData: BuiltInComponentData<
      typeof BuiltInComponentTypes.worldPortal
    >
  ) {
    this.componentData = componentData;
    const texture = textureLoadingService.getOrLoadTexture(
      componentData.params.textureUri,
      { repeat: true }
    );

    const orb = createPortalOrb({
      radius: 0.4,
      height: 1.5,
      envMap: texture,
      offsetX: 0,
      textureDistortionAmount: 0.05,
      textureDistortionTimeScale: 0,
      rippleNoiseScale: 0.5,
      timeScale: 0.2,
      glowColor: "#58f287",
      glowIntensity: 0.8,
    });

    orb.startTimeLoop();
    this.orb = orb;
  }

  onComponentParamsChange(
    updates: Partial<
      BuiltInComponentData<typeof BuiltInComponentTypes.worldPortal>["params"]
    >
  ) {
    if (updates.textureUri) {
      const texture = textureLoadingService.getOrLoadTexture(
        updates.textureUri,
        { repeat: true }
      );
      this.orb.updateProps({
        envMap: texture,
        textureDistortionAmount: 0.05,
        textureDistortionTimeScale: 2,
        rippleNoiseScale: 0.5,
        timeScale: 0.3,
        glowIntensity: 1,
      });
    }
  }

  dispose() {
    this.orb.stopTimeLoop();
    this.orb.mesh.clear();
  }
}
