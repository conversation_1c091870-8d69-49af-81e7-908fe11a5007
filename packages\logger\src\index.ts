import * as winston from "winston";
import { LoggingWinston } from "@google-cloud/logging-winston";

const transports: winston.transport =
  process.env.LOG_TO_CLOUD_LOGGING === "true"
    ? new LoggingWinston({
        redirectToStdout: true,
        useMessageField: false,
      })
    : new winston.transports.Console({
        format: winston.format.cli(),
      });

export const rootLogger = winston.createLogger({
  level: "debug",
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports,
});

export function getLogger(options?: object) {
  return options ? rootLogger.child(options) : rootLogger;
}
