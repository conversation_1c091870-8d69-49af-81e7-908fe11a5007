import { ULID } from "ulid";
import { EntityId } from "./entity";
import { EntityLocation } from "./world";
import { Component } from "./component";
import { SparseSet } from "./sparseset";

/**
 * Cells represent a single component in an archetype storing a component of the specified type.
 *
 * Cells may be either dense, storing a required component type, and thus store values forall rows (entities) in the archetype
 * or sparse, storing a sparse component type, and thus store values for a subset of rows (entities) in the archetype.
 */
export abstract class BaseCell {
  component: Component<unknown>;
  lastChangeTick: number;
  changeSet: Uint32Array | null = null;

  constructor(component: Component<unknown>) {
    this.component = component;
    this.lastChangeTick = 0;
  }

  abstract enableGranularChangeTracking(): void;

  granularChanges(): Uint32Array | null {
    return this.changeSet;
  }

  validateChangeSet(expectedLength: number) {
    if (this.changeSet === null) {
      return;
    }

    if (this.changeSet.length < expectedLength) {
      throw new Error(
        `Change set length mismatch ${this.changeSet.length} < ${expectedLength}`
      );
    }

    for (let i = 0; i < expectedLength; i++) {
      if (this.changeSet[i] === -1) {
        throw new Error("Change set has -1");
      }
    }
  }

  markDirty(tick: number, slot: number) {
    this.lastChangeTick = Math.max(this.lastChangeTick, tick);

    if (this.changeSet !== null) {
      if (this.changeSet.length <= slot) {
        const newArray = new Uint32Array(slot + 1);
        newArray.fill(-1);
        newArray.set(this.changeSet, 0);
        this.changeSet = newArray;
      }

      this.changeSet[slot] = tick;
    }
  }

  abstract get(index: number): unknown;
}

export class DenseCell extends BaseCell {
  private _data: unknown[];

  constructor(component: Component<unknown>, data: unknown[] = []) {
    super(component);
    this._data = data;
  }

  enableGranularChangeTracking(): void {
    if (this.changeSet === null) {
      this.changeSet = new Uint32Array(this._data.length);
    }
  }

  get(index: number): unknown {
    return this._data[index];
  }

  set(index: number, value: unknown) {
    this._data[index] = value;
  }

  push(value: unknown) {
    this._data.push(value);
  }

  swapRemove(index: number) {
    const lastIndex = this._data.length - 1;
    this._data[index] = this._data[lastIndex];

    if (this.changeSet) {
      // Change set may be larger, but is then filled with -1, this it to avoid constant shrinking of of Uint32Array
      if (this.changeSet.length < this._data.length) {
        throw new Error(
          `Change set length mismatch ${this.changeSet.length} != ${this._data.length}`
        );
      }

      this.changeSet[index] = this.changeSet[lastIndex];
      this.changeSet[lastIndex] = -1;
    }

    this._data.pop();
  }

  length(): number {
    return this._data.length;
  }
}

export class SparseCell extends BaseCell {
  private _data: SparseSet<unknown>;

  constructor(component: Component<unknown>) {
    super(component);
    this._data = new SparseSet();
  }

  enableGranularChangeTracking(): void {
    if (this.changeSet === null) {
      this.changeSet = new Uint32Array(this._data.maxIndex());
    }
  }

  get(index: number): unknown {
    return this._data.get(index);
  }

  set(index: number, value: unknown): boolean {
    const added = this._data.insert(index, value);
    return added;
  }

  /** Returns the number of *sparse* components currently set, not the number of entities */
  count(): number {
    return this._data.length();
  }

  swapRemove(index: number, maxEntity: number): boolean {
    const removed = this._data.remove(index);
    // swap last index with the one we are removing
    this._data.insert(index, this._data.get(maxEntity));
    this._data.remove(maxEntity);

    if (this.changeSet) {
      this.changeSet[index] = this.changeSet[maxEntity] || 0;
      this.changeSet[maxEntity] = 0;
    }

    return removed;
  }
}

export type ComponentKey = number;

/** Archetype store all entities with the same set of components
 *
 * This "data-uniformity" ensures efficient memory usage and iteration performance as the data is of the same type, and stored contiguously.
 *
 * An archetype may *optionally* contain *sparse* components, that may or not be present on a per entity basis. This is to reduce many small archetypes,
 * and instead have a few large ones, at the cost of reduced iteration performance for the sparse components.
 */
export class Archetype {
  private _cells: Map<ComponentKey, DenseCell>;
  private _sparseComponents: Map<ComponentKey, SparseCell>;

  private _entities: EntityId[];

  constructor(requiredComponents: Component<unknown>[]) {
    this._cells = new Map();

    for (const component of requiredComponents) {
      assertRequired(component);

      this._cells.set(component.KEY, new DenseCell(component));
    }

    this._entities = [];
    this._sparseComponents = new Map();
  }

  public entityCount(): number {
    return this._entities.length;
  }

  public cells(): IterableIterator<DenseCell> {
    return this._cells.values();
  }

  public get entities(): EntityId[] {
    return this._entities;
  }

  public hasComponentForEntity<T>(
    index: number,
    component: Component<T>
  ): boolean {
    if (component.meta?.sparse) {
      return this.hasSparseComponentForEntity(index, component);
    } else {
      return this.hasRequiredComponent(component);
    }
  }

  public hasRequiredComponent<T>(component: Component<T>): boolean {
    assertRequired(component);
    return this._cells.has(component.KEY);
  }

  /** Sparse components *may* be present for an entity in the archetype */
  public hasSparseComponent(component: Component<unknown>): boolean {
    assertSparse(component);
    return this._sparseComponents.has(component.KEY);
  }

  public sparseComponents(): IterableIterator<ComponentKey> {
    return this._sparseComponents.keys();
  }

  public sparseCells(): IterableIterator<SparseCell> {
    return this._sparseComponents.values();
  }

  public addEntity(entity: EntityId): number {
    this._entities.push(entity);
    for (const cell of this._cells.values()) {
      cell.push(undefined);
    }

    return this._entities.length - 1;
  }

  public cleanSparseCells() {
    for (const sparseCell of [...this._sparseComponents.values()]) {
      if (sparseCell.count() === 0) {
        this._sparseComponents.delete(sparseCell.component.KEY);
      }
    }
  }

  public removeEntity(entity: EntityId, locations: Map<ULID, EntityLocation>) {
    const loc = locations.get(entity)!;

    if (loc.archetype !== this) {
      throw new Error(`Entity ${entity} does not exist in this archetype`);
    }

    for (const cell of this._cells.values()) {
      cell.swapRemove(loc.index);
      cell.validateChangeSet(this._entities.length - 1);
    }

    for (const sparseCell of this._sparseComponents.values()) {
      sparseCell.swapRemove(loc.index, this._entities.length - 1);
      if (sparseCell.count() === 0) {
        this._sparseComponents.delete(sparseCell.component.KEY);
      }
      sparseCell.validateChangeSet(this._entities.length - 1);
    }

    const oldIndex = loc.index;
    this._entities[oldIndex] = this._entities[this._entities.length - 1];
    this._entities.pop();

    if (oldIndex != this._entities.length) {
      locations.set(this._entities[oldIndex], {
        archetype: this,
        index: oldIndex,
      });
    }
    locations.delete(entity);
  }

  /** Moves an entity to another archetype */
  public moveEntity(
    entity: EntityId,
    locations: Map<ULID, EntityLocation>,
    target: Archetype
  ): EntityLocation {
    this.assertIsComplete();

    if (target === this) {
      throw new Error("Can not move archetype into itself");
    }

    const loc = locations.get(entity);

    if (loc === undefined) {
      throw new Error(`Entity ${entity} not found in archetype`);
    }

    if (loc.archetype !== this) {
      throw new Error(`Entity ${entity} not found in archetype`);
    }

    const newIndex = target.addEntity(entity);

    const oldIndex = loc.index;

    if (this._entities[oldIndex] !== entity) {
      throw new Error("Entity mismatch");
    }

    if (this._entities.length == 0) {
      throw new Error("Archetype is empty");
    }

    // Remove the hole and move each value of the columns to the new archetype
    for (const cell of this._cells.values()) {
      const data = cell.get(oldIndex);

      if (target.hasRequiredComponent(cell.component)) {
        target.setRequiredComponent(newIndex, cell.component, data, null);

        // carry over granular changes
        const targetCell = target.getCell(cell.component);
        if (targetCell) {
          if (cell.changeSet !== null) {
            targetCell.enableGranularChangeTracking();
            const oldTick = cell.changeSet![oldIndex];
            targetCell.markDirty(oldTick, newIndex);
          }
          // previous archetype did not track changes for this component, so we can't know any precise tick
          else if (cell.changeSet === null) {
            targetCell.markDirty(cell.lastChangeTick, newIndex);
          }

          targetCell.lastChangeTick = Math.max(
            targetCell.lastChangeTick,
            cell.lastChangeTick
          );
        }

        cell.validateChangeSet(this._entities.length - 1);
      }

      cell.swapRemove(oldIndex);
    }

    // transfer all optional components
    for (const cell of this._sparseComponents.values()) {
      target.setSparseComponent(
        newIndex,
        cell.component,
        cell.get(oldIndex),
        null
      );

      // carry over granular changes
      const targetCell = target.getCell(cell.component)!;
      if (cell.changeSet !== null) {
        targetCell.enableGranularChangeTracking();
        const oldTick = cell.changeSet![oldIndex];
        targetCell.markDirty(oldTick, newIndex);
      } else if (cell.changeSet === null) {
        targetCell.markDirty(cell.lastChangeTick, newIndex);
      }

      targetCell.lastChangeTick = Math.max(
        targetCell.lastChangeTick,
        cell.lastChangeTick
      );

      cell.validateChangeSet(this._entities.length - 1);

      cell.swapRemove(oldIndex, this._entities.length - 1);

      // ensure we delete unneeded sparse cells, this improves query iteration time when traversing sparse components
      if (cell.count() === 0) {
        this._sparseComponents.delete(cell.component.KEY);
      }
    }

    this._entities[oldIndex] = this._entities[this._entities.length - 1];
    this._entities.pop();

    const newLocation = {
      archetype: target,
      index: newIndex,
    };

    locations.set(entity, newLocation);

    if (oldIndex != this._entities.length) {
      const swappedEntity: EntityId = this._entities[oldIndex];
      if (locations.get(swappedEntity)!.archetype !== this) {
        throw new Error("Entity archetype mismatch");
      }

      locations.get(swappedEntity)!.index = oldIndex;
    }

    return newLocation;
  }

  public getCell(key: Component<unknown>): BaseCell | undefined {
    if (key.meta?.sparse) {
      return this.getSparseCell(key);
    } else {
      return this.getDenseCell(key);
    }
  }

  public getDenseCell(key: Component<unknown>): DenseCell | undefined {
    assertRequired(key);
    return this._cells.get(key.KEY);
  }

  public getSparseCell(key: Component<unknown>): SparseCell | undefined {
    assertSparse(key);
    return this._sparseComponents.get(key.KEY);
  }

  private getOrInitSparseCell(component: Component<unknown>): SparseCell {
    assertSparse(component);
    let cell = this._sparseComponents.get(component.KEY);
    if (!cell) {
      cell = new SparseCell(component);
      this._sparseComponents.set(component.KEY, cell);
    }

    return cell;
  }

  public getRequiredComponent<T>(
    index: number,
    component: Component<T>
  ): T | null {
    assertRequired(component);
    const cell = this.getDenseCell(component);

    // if not required, there may be an optional components present for the entity
    if (!cell) {
      return null;
    }

    return cell.get(index) as T;
  }

  public getComponent<T>(index: number, component: Component<T>): T | null {
    if (component.meta?.sparse) {
      return this.getSparseComponent(index, component);
    } else {
      return this.getRequiredComponent(index, component);
    }
  }

  public setRequiredComponent<T>(
    index: number,
    component: Component<T>,
    value: T,
    tick: number | null
  ): boolean {
    assertRequired(component);
    const cell = this.getDenseCell(component);
    if (!cell) {
      throw new Error(`Component ${component.NAME} not found in archetype`);
    }

    const added = cell.get(index) === undefined;
    cell.set(index, value);

    if (tick !== null) {
      cell.markDirty(tick, index);
    }

    return added;
  }

  public removeSparseComponent<T>(
    index: number,
    component: Component<T>
  ): boolean {
    assertSparse(component);
    const cell = this.getOrInitSparseCell(component);
    const removed = cell.swapRemove(index, this._entities.length - 1);

    if (cell.count() === 0) {
      this._sparseComponents.delete(component.KEY);
    }

    return removed;
  }

  public getSparseComponent<T>(
    index: number,
    component: Component<T>
  ): T | null {
    assertSparse(component);
    const sparseCell = this.getSparseCell(component);
    return sparseCell?.get(index) as T | null;
  }

  public hasSparseComponentForEntity(
    index: number,
    component: Component<unknown>
  ): boolean {
    assertSparse(component);
    return this.getSparseCell(component)?.get(index) != null;
  }

  public setComponent<T>(
    index: number,
    component: Component<T>,
    value: T,
    tick: number | null
  ): boolean {
    if (component.meta?.sparse) {
      return this.setSparseComponent(index, component, value, tick);
    } else {
      return this.setRequiredComponent(index, component, value, tick);
    }
  }

  public setSparseComponent<T>(
    index: number,
    component: Component<T>,
    value: T,
    tick: number | null
  ): boolean {
    assertSparse(component);
    const sparseCell = this.getOrInitSparseCell(component);
    const added = sparseCell.set(index, value);

    if (tick !== null) {
      sparseCell.markDirty(tick, index);
    }

    return added;
  }

  public markDirty(
    tick: number,
    index: number,
    component: Component<unknown>
  ): void {
    if (component.meta?.sparse) {
      this.getOrInitSparseCell(component).markDirty(tick, index);
    } else {
      this.getDenseCell(component)?.markDirty(tick, index);
    }
  }

  assertIsComplete() {
    for (const cell of this._cells.values()) {
      for (let i = 0; i < cell.length(); i++) {
        if (cell.get(i) === undefined) {
          throw new Error(
            `Cell ${cell.component.NAME} has undefined data, but there are ${this._entities.length} entities`
          );
        }
      }

      if (cell.length() !== this._entities.length) {
        throw new Error(
          `Cell ${cell.component.NAME} has ${cell.length()} data, but there are ${this._entities.length} entities`
        );
      }
    }
  }
}

function assertSparse<T>(component: Component<T>) {
  if (!component.meta?.sparse) {
    throw new Error(
      "Expected a sparse component, but got a required component"
    );
  }
}

function assertRequired<T>(component: Component<T>) {
  if (component.meta?.sparse) {
    throw new Error(
      "Expected a required component, but got a sparse component"
    );
  }
}
