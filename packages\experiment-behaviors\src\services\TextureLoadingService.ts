import { RepeatWrapping, Texture, TextureLoader } from "three";

function createTextureLoadingService() {
  const textureCache = new Map<string, Texture>();
  const textureLoader = new TextureLoader();

  const getOrLoadTexture = (
    url: string,
    options?: { repeat?: boolean }
  ): Texture => {
    if (!textureCache.has(url)) {
      const tex = textureLoader.load(url);
      if (options?.repeat) {
        tex.wrapS = tex.wrapT = RepeatWrapping;
      }
      textureCache.set(url, tex);
    }
    return textureCache.get(url)!;
  };

  const dispose = () => {
    textureCache.forEach((texture) => texture.dispose());
    textureCache.clear();
  };

  return {
    getOrLoadTexture,
    dispose,
  };
}

export type TextureLoadingService = ReturnType<
  typeof createTextureLoadingService
>;

// Export singleton instance
export const textureLoadingService = createTextureLoadingService();
