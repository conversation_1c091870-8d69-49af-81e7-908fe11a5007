import { Color, Mesh, MeshStandardMaterial, Object3D } from "three";

import {
  createMaterialFactoryService,
  type MaterialPreset,
} from "@nilo/experiment-behaviors/services/MaterialFactoryService";
import { PropertyOverrideService } from "@nilo/experiment-behaviors/services/PropertyOverrideService";
import { DisplayModelsFromUriSystem } from "@nilo/experiment-behaviors/systems/DisplayModelsFromUriSystem";
import { BuiltInComponentTypes } from "@nilo/experiment-behaviors/types/BuiltInComponentData";
import { createMulticaster } from "@nilo/experiment-behaviors/utils/createMulticaster";

import type { EcsSystemContext } from "@nilo/experiment-behaviors/system-context/createSystemContext";
import type { BuiltInComponentData } from "@nilo/experiment-behaviors/types/BuiltInComponentData";
import type { EcsSystem } from "@nilo/experiment-behaviors/types/EcsSystem";
import type { WorldEntityUUID } from "@nilo/experiment-behaviors/types/NilusEntityData";

const MATERIAL_OVERRIDE_SOURCE = "MaterialOverrideSystem";

export class OverrideMaterialSystem implements EcsSystem {
  private readonly controllers = new Map<
    WorldEntityUUID,
    MaterialOverrideController
  >();
  private readonly onDispose = createMulticaster();

  private readonly propertyOverrideService = new PropertyOverrideService();
  private readonly materialFactory = createMaterialFactoryService();

  initialize(context: EcsSystemContext) {
    const { events } = context;

    const modelSystemApi = //
      context.getRequiredSystemApiByConstructor(DisplayModelsFromUriSystem);

    // Handle model loading
    modelSystemApi.events.onModelLoaded.add(
      (event: { entityUuid: WorldEntityUUID; modelObject: Object3D }) => {
        const { entityUuid, modelObject } = event;
        const controller = this.controllers.get(entityUuid);
        if (controller) {
          controller.onModelLoaded(modelObject);
        }
      }
    );

    // Handle component lifecycle
    events.onEntityComponentAddedByType.add(
      BuiltInComponentTypes.materialOverride,
      (_context, entityData, componentData) => {
        // If a controller already exists, dispose it first
        const existingController = this.controllers.get(entityData.uuid);
        if (existingController) {
          existingController.dispose();
        }

        const controller = new MaterialOverrideController(
          entityData.uuid,
          componentData,
          this.propertyOverrideService,
          this.materialFactory
        );

        this.controllers.set(entityData.uuid, controller);

        // Apply to any existing model
        const model = modelSystemApi.getLoadedModel(entityData.uuid);
        if (model) {
          controller.onModelLoaded(model);
        }
      }
    );

    events.onEntityComponentRemovedByType.add(
      BuiltInComponentTypes.materialOverride,
      (_context, entityData) => {
        const controller = this.controllers.get(entityData.uuid);
        if (controller) {
          // If we have a model, remove the override
          const model = modelSystemApi.getLoadedModel(entityData.uuid);
          if (model) {
            controller.removeFromModel(model);
          }
          controller.dispose();
          this.controllers.delete(entityData.uuid);
        }
      }
    );

    events.onEntityComponentUpdatedByType.add(
      BuiltInComponentTypes.materialOverride,
      (_context, entityData, componentData) => {
        const controller = this.controllers.get(entityData.uuid);
        if (!controller) {
          console.warn(
            "🔶 Entity has no material override controller, skipping:",
            entityData.uuid
          );
          return;
        }

        controller.onComponentParamsChange(componentData.params);

        // Reapply to model if it exists
        const model = modelSystemApi.getLoadedModel(entityData.uuid);
        if (model) {
          controller.removeFromModel(model);
          controller.onModelLoaded(model);
        }
      }
    );
  }

  dispose() {
    for (const controller of this.controllers.values()) {
      controller.dispose();
    }
    this.controllers.clear();
    this.materialFactory.dispose();
    this.onDispose.invoke();
  }
}

class MaterialOverrideController {
  private material?: MeshStandardMaterial;
  private overrideId?: string;
  private readonly models = new Set<Object3D>();

  constructor(
    private readonly entityUuid: WorldEntityUUID,
    private readonly componentData: BuiltInComponentData<
      typeof BuiltInComponentTypes.materialOverride
    >,
    private readonly propertyOverrideService: PropertyOverrideService,
    private readonly materialFactory: ReturnType<
      typeof createMaterialFactoryService
    >,
    private readonly defaultColor = new Color(0xcccccc)
  ) {}

  onModelLoaded(modelObject: Object3D) {
    this.models.add(modelObject);
    this.applyToModel(modelObject);
  }

  applyToModel(modelObject: Object3D) {
    if (!this.material) {
      this.material = this.materialFactory.getMaterial(
        this.componentData.params.preset as MaterialPreset,
        this.componentData.params.color ?? this.defaultColor
      ) as MeshStandardMaterial;
    }

    modelObject.traverse((object) => {
      if (object instanceof Mesh) {
        // Only apply override if we haven't already overridden this mesh
        if (
          !this.propertyOverrideService.hasOverrideFromSource(
            object,
            "material",
            MATERIAL_OVERRIDE_SOURCE
          )
        ) {
          this.overrideId = this.propertyOverrideService.addPropertyOverride(
            object,
            "material",
            this.material!,
            MATERIAL_OVERRIDE_SOURCE
          );

          // Attempt to compute vertex normals if geometry exists and is BufferGeometry
          if (object.geometry && object.geometry.isBufferGeometry) {
            object.geometry.computeVertexNormals();
          }
        }
      }
    });
  }

  removeFromModel(modelObject: Object3D) {
    if (this.overrideId) {
      modelObject.traverse((object) => {
        if (object instanceof Mesh) {
          this.propertyOverrideService.removePropertyOverride(
            object,
            "material",
            this.overrideId!
          );
        }
      });
      this.overrideId = undefined;
    }
  }

  onComponentParamsChange(
    updates: Partial<
      BuiltInComponentData<
        typeof BuiltInComponentTypes.materialOverride
      >["params"]
    >
  ) {
    // If material params changed, dispose old material and create new one
    if (updates.preset || updates.color) {
      if (this.material) {
        this.material.dispose();
        this.material = undefined;
      }
    }
  }

  dispose() {
    // Remove material override from all tracked models
    for (const model of this.models) {
      this.removeFromModel(model);
    }
    this.models.clear();

    if (this.material) {
      this.material.dispose();
      this.material = undefined;
    }
  }
}
