import {
  AdditiveBlending,
  Buff<PERSON>Geometry,
  Color,
  Float32BufferAttribute,
  NormalBlending,
  Points,
  PointsMaterial,
  Texture,
  TextureLoader,
  Vector3,
} from "three";

import { ThreejsSceneSystem } from "@nilo/experiment-behaviors/systems/ThreejsSceneSystem";
import { textureLoadingService } from "@nilo/experiment-behaviors/services/TextureLoadingService";
import { createMulticaster } from "@nilo/experiment-behaviors/utils/createMulticaster";

import type { EcsSystemContext } from "@nilo/experiment-behaviors/system-context/createSystemContext";
import {
  BuiltInComponentTypes,
  type BuiltInComponentData,
} from "@nilo/experiment-behaviors/types/BuiltInComponentData";
import type {
  EcsSystem,
  EcsSystemApi,
} from "@nilo/experiment-behaviors/types/EcsSystem";
import type { WorldEntityUUID } from "@nilo/experiment-behaviors/types/NilusEntityData";

export class DisplayPointParticleSystem implements EcsSystem {
  private readonly textureLoader = new TextureLoader();
  private readonly textureCache = new Map<string, Texture>();
  private readonly particleControllers = new Map<
    WorldEntityUUID,
    ParticleEmitter[]
  >();
  private readonly onDispose = createMulticaster();

  initialize({ events }: EcsSystemContext) {
    events.onEntityComponentAddedByType.add(
      BuiltInComponentTypes.pointParticleEmitter,
      (context, entityData, componentData) => {
        const sceneApi =
          context.getRequiredSystemApiByConstructor(ThreejsSceneSystem);

        const controllers = this.particleControllers.get(entityData.uuid) ?? [];
        const ctrl = new ParticleEmitter(
          entityData.uuid,
          componentData,
          sceneApi,
          this.textureLoader,
          this.textureCache
        );

        controllers.push(ctrl);
        this.particleControllers.set(entityData.uuid, controllers);
        ctrl.addToScene();

        ctrl.setPosition(entityData.transform.position);

        console.debug(
          "💨 Added particle emitter for entity:",
          entityData.uuid,
          "component:",
          componentData.uuid
        );
      }
    );

    events.onEntityComponentRemovedByType.add(
      BuiltInComponentTypes.pointParticleEmitter,
      (_context, entityData) => {
        const controllers = this.particleControllers.get(entityData.uuid);
        if (controllers) {
          for (const ctrl of controllers) {
            ctrl.dispose();
          }
          this.particleControllers.delete(entityData.uuid);
          console.debug(
            "💨 Removed particle emitters for entity:",
            entityData.uuid
          );
        }
      }
    );

    events.onEntityComponentUpdatedByType.add(
      BuiltInComponentTypes.pointParticleEmitter,
      (_context, entityData, componentData) => {
        const controllers = this.particleControllers.get(entityData.uuid);
        if (!controllers) {
          console.warn(
            "🔶 Entity has no particle controllers, skipping:",
            entityData.uuid
          );
          return;
        }

        const ctrl = controllers.find(
          (c) => c.componentData.uuid === componentData.uuid
        );
        if (ctrl) {
          ctrl.onComponentParamsChange(componentData.params);
        }
      }
    );

    events.onEntityTransformUpdated.add((_context, entityData) => {
      const controllers = this.particleControllers.get(entityData.uuid);
      if (controllers) {
        for (const ctrl of controllers) {
          ctrl.setPosition(entityData.transform.position);
        }
      }
    });

    events.onFrame.add((_context, delta) => {
      for (const controllers of this.particleControllers.values()) {
        for (const ctrl of controllers) {
          ctrl.onEnterFrame(delta);
        }
      }
    });

    console.debug("💨 ParticleSystem: Initialized");
  }

  dispose() {
    for (const controllers of this.particleControllers.values()) {
      for (const ctrl of controllers) {
        ctrl.dispose();
      }
    }
    this.particleControllers.clear();
    this.textureCache.forEach((texture) => texture.dispose());
    this.textureCache.clear();
    this.onDispose.invoke();
    console.debug("💨 ParticleSystem: Disposed");
  }
}

class ParticleEmitter {
  private readonly particles: Particle[] = [];
  private readonly pointsObject: Points;
  private readonly material: PointsMaterial;
  private readonly geometry: BufferGeometry;
  private readonly baseColor: Color;
  private timeSinceLastEmission = 0;

  constructor(
    private readonly entityUuid: WorldEntityUUID,
    public readonly componentData: BuiltInComponentData<
      typeof BuiltInComponentTypes.pointParticleEmitter
    >,
    private readonly sceneApi: EcsSystemApi<ThreejsSceneSystem>,
    private readonly textureLoader: TextureLoader,
    private readonly textureCache: Map<string, Texture>
  ) {
    const params = componentData.params;
    this.geometry = new BufferGeometry();
    this.baseColor = new Color(params.color);

    this.material = new PointsMaterial({
      size: params.size,
      map: textureLoadingService.getOrLoadTexture(params.textureUri),
      blending:
        params.blending === "additive" ? AdditiveBlending : NormalBlending,
      depthWrite: false,
      transparent: true,
      vertexColors: true,
    });

    this.pointsObject = new Points(this.geometry, this.material);
    this.pointsObject.frustumCulled = false;
  }

  setPosition(position: readonly [number, number, number]) {
    this.pointsObject.position.set(...position);
  }

  onEnterFrame(delta: number) {
    const params = this.componentData.params;
    const emitterWorldPosition = new Vector3();

    this.timeSinceLastEmission += delta;
    const emissionInterval = 1 / params.emissionRate;
    while (
      this.timeSinceLastEmission > emissionInterval &&
      this.particles.length < params.maxParticles
    ) {
      this.timeSinceLastEmission -= emissionInterval;

      // Calculate randomized start and end velocities
      const rStartVelocity = new Vector3(
        params.velocityStart[0] +
          (Math.random() - 0.5) * 2 * params.velocityRandomness[0],
        params.velocityStart[1] +
          (Math.random() - 0.5) * 2 * params.velocityRandomness[1],
        params.velocityStart[2] +
          (Math.random() - 0.5) * 2 * params.velocityRandomness[2]
      );
      const rEndVelocity = new Vector3(
        params.velocityEnd[0] +
          (Math.random() - 0.5) * 2 * params.velocityRandomness[0],
        params.velocityEnd[1] +
          (Math.random() - 0.5) * 2 * params.velocityRandomness[1],
        params.velocityEnd[2] +
          (Math.random() - 0.5) * 2 * params.velocityRandomness[2]
      );

      // Get random position based on emitter shape
      const spawnPosition = getRandomPointInShape(
        params.emitterShape,
        emitterWorldPosition
      );
      spawnPosition.add(emitterWorldPosition);

      const newParticle: Particle = {
        position: spawnPosition,
        baseStartVelocity: rStartVelocity,
        baseEndVelocity: rEndVelocity,
        currentVelocity: rStartVelocity.clone(),
        age: 0,
        lifetime: params.particleLifetime,
      };
      this.particles.push(newParticle);
    }

    const positions: number[] = [];
    const colors: number[] = [];

    for (let i = this.particles.length - 1; i >= 0; i--) {
      const p = this.particles[i];
      p.age += delta;

      if (p.age >= p.lifetime) {
        this.particles.splice(i, 1);
        continue;
      }

      const lifeRatio = p.age / p.lifetime;

      const interpolatedBaseVelocity = new Vector3().lerpVectors(
        p.baseStartVelocity,
        p.baseEndVelocity,
        lifeRatio
      );
      p.currentVelocity.copy(interpolatedBaseVelocity);

      if (params.gravity) {
        p.currentVelocity.x += params.gravity[0] * delta;
        p.currentVelocity.y += params.gravity[1] * delta;
        p.currentVelocity.z += params.gravity[2] * delta;
      }

      p.position.addScaledVector(p.currentVelocity, delta);
      positions.push(p.position.x, p.position.y, p.position.z);

      const currentAlpha = lerp(params.alphaStart, params.alphaEnd, lifeRatio);
      colors.push(
        this.baseColor.r,
        this.baseColor.g,
        this.baseColor.b,
        currentAlpha
      );
    }

    if (positions.length > 0) {
      this.geometry.setAttribute(
        "position",
        new Float32BufferAttribute(positions, 3)
      );
      this.geometry.setAttribute(
        "color",
        new Float32BufferAttribute(colors, 4)
      );
      this.geometry.computeBoundingSphere();
    } else {
      this.geometry.deleteAttribute("position");
      this.geometry.deleteAttribute("color");
    }
    this.pointsObject.visible = positions.length > 0;
  }

  onComponentParamsChange(updates: Partial<PointParticleEmitterParams>) {
    const params = { ...this.componentData.params, ...updates };
    this.material.size = params.size;
    this.material.map = textureLoadingService.getOrLoadTexture(
      params.textureUri
    );
    this.material.blending =
      params.blending === "additive" ? AdditiveBlending : NormalBlending;
    this.baseColor.set(params.color);
  }

  addToScene() {
    this.sceneApi.getScene().add(this.pointsObject);
  }

  removeFromScene() {
    this.sceneApi.getScene().remove(this.pointsObject);
  }

  dispose() {
    this.removeFromScene();
    this.geometry.dispose();
    this.material.dispose();
  }
}

function lerp(a: number, b: number, t: number): number {
  return a * (1 - t) + b * t;
}

interface Particle {
  position: Vector3;
  // Store randomized start/end values per particle
  currentVelocity: Vector3; // The velocity used and modified by gravity each frame
  baseStartVelocity: Vector3; // Randomized start velocity, before gravity
  baseEndVelocity: Vector3; // Randomized end velocity, before gravity
  age: number;
  lifetime: number;
}

type PointParticleEmitterParams = BuiltInComponentData<
  typeof BuiltInComponentTypes.pointParticleEmitter
>["params"];

// Helper functions for emitter shapes
function parseShapeParams(shapeStr: string): {
  type: string;
  params: number[];
} {
  const [type, paramsStr] = shapeStr.split(":");
  const params = paramsStr.split(",").map(Number);
  return { type, params };
}

function getRandomPointInShape(
  shapeStr: string | undefined,
  basePosition: Vector3
): Vector3 {
  const { type, params } = parseShapeParams(shapeStr ?? "point:0,0,0");
  const result = new Vector3();

  switch (type) {
    case "point": {
      result.set(params[0], params[1], params[2]);
      break;
    }
    case "box": {
      result.set(
        lerp(params[0], params[3], Math.random()),
        lerp(params[1], params[4], Math.random()),
        lerp(params[2], params[5], Math.random())
      );
      break;
    }
    case "ellipsoid": {
      // Generate random point in unit sphere and scale by radius
      const theta = Math.random() * Math.PI * 2;
      const phi = Math.acos(2 * Math.random() - 1);
      const r = Math.cbrt(Math.random()); // Cube root for uniform distribution

      result.set(
        params[0] + r * Math.sin(phi) * Math.cos(theta) * params[3],
        params[1] + r * Math.sin(phi) * Math.sin(theta) * params[4],
        params[2] + r * Math.cos(phi) * params[5]
      );
      break;
    }
    default: {
      console.warn("🔴 ParticleSystem: Unknown emitter shape:", type);
      result.copy(basePosition);
    }
  }

  return result;
}
