import { Archetype } from "./archetype";
import { Component } from "./component";
import { EntityData } from "./entityData";
import { EntityLocation, World } from "./world";

/**
 * Represents a pattern of components to match against.
 *
 * Supports both object patterns and array/tuple patterns.
 *
 * @example
 * const pattern: QueryPattern = {
 *   pos: Position,
 *   vel: Velocity,
 * };
 *
 * @example
 * const pattern: QueryPattern = [Name, Position];
 */
export type QueryPattern<T extends Component<unknown> = Component<unknown>> =
  | { [componentName: string]: T }
  | readonly T[];

export type QueryData<T extends QueryPattern> = {
  [K in keyof T]: T[K] extends Component<infer U> ? U : never;
};

export function matchesArchetype(
  pattern: QueryPattern,
  archetype: Archetype
): boolean {
  // NOTE: only filter on dense, since adding a sparse component by design does not force a new archetype tick
  if (Array.isArray(pattern)) {
    return pattern.every(
      (component) =>
        component.meta?.sparse || archetype.hasRequiredComponent(component)
    );
  }

  return Object.values(pattern).every(
    (component) =>
      component.meta?.sparse || archetype.hasRequiredComponent(component)
  );
}

export function matchesEntity(
  pattern: QueryPattern,
  entityLocation: EntityLocation,
  extraComponents?: Component<unknown>[]
): boolean {
  if (Array.isArray(pattern)) {
    return pattern.every((component) => {
      if (component.meta?.sparse) {
        return (
          entityLocation.archetype.hasSparseComponentForEntity(
            entityLocation.index,
            component
          ) || extraComponents?.includes(component)
        );
      }
      return (
        entityLocation.archetype.hasRequiredComponent(component) ||
        extraComponents?.includes(component)
      );
    });
  }

  return Object.values(pattern).every((component) => {
    if (component.meta?.sparse) {
      return (
        entityLocation.archetype.hasSparseComponentForEntity(
          entityLocation.index,
          component
        ) || extraComponents?.includes(component)
      );
    } else {
      return (
        entityLocation.archetype.hasRequiredComponent(component) ||
        extraComponents?.includes(component)
      );
    }
  });
}

export function isRequiredByPattern(
  pattern: QueryPattern,
  component: Component<unknown>
): boolean {
  if (Array.isArray(pattern)) {
    return pattern.some((c) => c === component);
  }

  return Object.values(pattern).some((c) => c === component);
}

export function extractSparseComponents(
  pattern: QueryPattern
): Component<unknown>[] {
  if (Array.isArray(pattern)) {
    return pattern.filter((component) => component.meta?.sparse);
  }
  return Object.values(pattern).filter((component) => component.meta?.sparse);
}

export function extractComponents(pattern: QueryPattern): Component<unknown>[] {
  if (Array.isArray(pattern)) {
    return pattern;
  }
  return Object.values(pattern);
}

/** Queries allow matching on relevant entities and iterating over the matched component values
 *
 * Prefer to keep queries around and reuse them as the archetypes to visit are cached.
 *
 * **NOTE**: when mutating a component, call `world.markDirty` to trigger a change tick.
 */
export class Query<T extends QueryPattern = QueryPattern> {
  private _archetypes: Archetype[];
  private _pattern: T;
  private _components: Component<unknown>[];
  private _sparseComponents: Component<unknown>[];
  // It is necessary to rediscover available archetypes when the change tick changes.
  private _archetypeTick: number;

  private _trackModified: boolean = false;
  private _changeTick: number = 0;

  constructor(pattern: T) {
    this._archetypes = [];
    this._pattern = pattern;
    this._components = extractComponents(pattern);
    this._sparseComponents = extractSparseComponents(pattern);
    this._archetypeTick = -1;
  }

  private refreshArchetypes(world: World) {
    this._archetypes.length = 0;
    for (const archetype of world.archetypes()) {
      if (matchesArchetype(this._pattern, archetype)) {
        this._archetypes.push(archetype);
      }
    }

    this._archetypeTick = world.archetypeTick;
  }

  public trackModified(): this {
    this._trackModified = true;
    return this;
  }

  public first(world: World): [EntityData, QueryData<T>] | null {
    let result: [EntityData, QueryData<T>] | null = null;
    this.forEach(world, (entity, data) => {
      result = [entity, data];
      return true;
    });
    return result ?? null;
  }

  public forEach(
    world: World,
    callback: (entity: EntityData, data: QueryData<T>) => void | boolean
  ) {
    const newChangeTick = world.changeTick;

    if (this._sparseComponents.length == 0 && !this.trackModified) {
      return this.forEachFast(world, callback);
    }

    const changeTick = this._trackModified ? this._changeTick : null;
    this._changeTick = world.changeTick;
    let stop = false;

    try {
      // Ensure entities do not move around while iterating
      world.acquireArchetypeLock();
      if (this._archetypeTick !== world.archetypeTick) {
        this.refreshArchetypes(world);
      }

      for (const archetype of this._archetypes) {
        if (stop) {
          break;
        }

        if (archetype.entityCount() === 0) {
          continue;
        }

        // conditionally skip entire archetypes.
        // NOTE: this could further be improved by archetype pagination

        let includeArchetype = true;
        if (this._sparseComponents.length > 0) {
          for (const sparseComponent of this._sparseComponents) {
            if (!archetype.hasSparseComponent(sparseComponent)) {
              includeArchetype = false;
              break;
            }
          }
        }

        if (!includeArchetype) {
          continue;
        }

        if (
          this._trackModified &&
          !findAnyModified(archetype, this._components, changeTick)
        ) {
          continue;
        }

        for (let i = 0; i < archetype.entities.length; i++) {
          const entity = archetype.entities[i];
          const entityData = new EntityData(
            entity,
            world,
            { archetype, index: i },
            world.locTick
          );

          if (Array.isArray(this._pattern)) {
            // Handle array pattern
            const elements = extractComponentsToArray(
              archetype,
              i,
              this._pattern,
              changeTick
            );

            if (elements != null) {
              if (callback(entityData, elements as QueryData<T>) === true) {
                stop = true;
                break;
              }
            }
          } else {
            // Handle object pattern
            const data = extractComponentsToObject(
              archetype,
              i,
              this._pattern,
              changeTick
            );

            if (data != null) {
              if (callback(entityData, data) === true) {
                stop = true;
                break;
              }
            }
          }
        }

        if (changeTick != null) {
          for (const component of this._components) {
            const cell = archetype.getCell(component)!;
            if (cell != null && cell.lastChangeTick >= changeTick) {
              cell.enableGranularChangeTracking();
            }
          }
        }
      }
    } finally {
      this._changeTick = newChangeTick;
      world.releaseArchetypeLock();
    }
  }

  /** Special variant of `forEach` that excludes all mechanisms for granular change tracking and sparse component retrieval, favoring specialization to pure dense-component iteration */
  private forEachFast(
    world: World,
    callback: (entity: EntityData, data: QueryData<T>) => void | boolean
  ) {
    const newChangeTick = world.changeTick;

    try {
      world.acquireArchetypeLock();
      if (this._archetypeTick !== world.archetypeTick) {
        this.refreshArchetypes(world);
      }
      let stop = false;

      for (const archetype of this._archetypes) {
        if (stop) {
          break;
        }

        for (let i = 0; i < archetype.entities.length; i++) {
          const entity = archetype.entities[i];
          const entityData = new EntityData(
            entity,
            world,
            { archetype, index: i },
            world.locTick
          );

          if (Array.isArray(this._pattern)) {
            // Handle array pattern
            const elements = extractDenseComponentsToArray(
              archetype,
              i,
              this._pattern
            );

            if (elements != null) {
              if (callback(entityData, elements as QueryData<T>) === true) {
                stop = true;
                break;
              }
            }
          } else {
            // Handle object pattern
            const data = extractDenseComponentsToObject(
              archetype,
              i,
              this._pattern
            );

            if (data != null) {
              if (callback(entityData, data) === true) {
                stop = true;
                break;
              }
            }
          }
        }
      }
    } finally {
      this._changeTick = newChangeTick;
      world.releaseArchetypeLock();
    }
  }
}

function findAnyModified(
  archetype: Archetype,
  components: Component<unknown>[],
  changeTick: number | null
): boolean {
  for (const component of components) {
    if (component.meta?.sparse) {
      const cell = archetype.getSparseCell(component)!;
      if (changeTick != null && cell.lastChangeTick > changeTick) {
        return true;
      }
    } else {
      const cell = archetype.getDenseCell(component)!;
      if (changeTick != null && cell.lastChangeTick > changeTick) {
        return true;
      }
    }
  }

  return false;
}

function extractComponentsToArray(
  archetype: Archetype,
  i: number,
  pattern: Array<Component<unknown>>,
  seenChangeTick: number | null
): unknown[] | null {
  const elements = [] as unknown[];

  let include = seenChangeTick == null;
  for (const component of pattern) {
    const cell = archetype.getCell(component)!;
    if (cell == null) {
      return null;
    }

    const changes = cell.granularChanges();
    if (
      !include &&
      seenChangeTick != null &&
      (changes == null || changes[i] > seenChangeTick)
    ) {
      include = true;
    }

    const value = cell.get(i);
    if (value == null) {
      return null;
    }

    elements.push(value);
  }

  return include ? elements : null;
}

function extractDenseComponentsToArray(
  archetype: Archetype,
  i: number,
  pattern: Array<Component<unknown>>
): unknown[] | null {
  const elements = [] as unknown[];

  for (const component of pattern) {
    elements.push(archetype.getRequiredComponent(i, component));
  }

  return elements;
}

function extractComponentsToObject<T extends QueryPattern>(
  archetype: Archetype,
  i: number,
  pattern: T,
  seenChangeTick: number | null
): QueryData<T> | null {
  const data = {} as QueryData<T>;

  let include = seenChangeTick == null;
  for (const [field, component] of Object.entries(pattern)) {
    const cell = archetype.getCell(component)!;
    if (cell == null) {
      return null;
    }

    const changes = cell.granularChanges();
    if (
      !include &&
      seenChangeTick != null &&
      (changes == null || changes[i] > seenChangeTick)
    ) {
      include = true;
    }

    const value = cell.get(i);
    if (value == null) {
      return null;
    }

    data[field as keyof T] = value as T[keyof T] extends Component<infer U>
      ? U
      : never;
  }

  return include ? data : null;
}

function extractDenseComponentsToObject<T extends QueryPattern>(
  archetype: Archetype,
  i: number,
  pattern: T
): QueryData<T> | null {
  const data = {} as QueryData<T>;
  for (const [field, component] of Object.entries(pattern)) {
    const value = archetype.getRequiredComponent(
      i,
      component
    ) as T[keyof T] extends Component<infer U> ? U : never;

    data[field as keyof T] = value;
  }

  return data;
}
