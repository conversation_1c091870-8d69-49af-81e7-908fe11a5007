import { getManhattanDistance } from "@nilo/experiment-behaviors/utils/used-by-systems/getManhattanDistance";

import type { EcsSystemContext } from "@nilo/experiment-behaviors/system-context/createSystemContext";
import type { EcsSystem } from "@nilo/experiment-behaviors/types/EcsSystem";
import type {
  WorldEntityData,
  WorldEntityUUID,
} from "@nilo/experiment-behaviors/types/NilusEntityData";

// Simplified type definitions with inference where possible
const DISTANCE_CALCULATORS = {
  manhattan: (a: WorldEntityData, b: WorldEntityData) =>
    getManhattanDistance(a.transform, b.transform),
} as const;

type DistanceMetric = keyof typeof DISTANCE_CALCULATORS;

type EntityFilter = (entity: WorldEntityData) => boolean;

type ProximityCallback = (otherEntity: WorldEntityData) => (() => void) | void;

class ProximitySensor {
  private readonly entitiesInRange = new Map<WorldEntityUUID, () => void>();
  private readonly subscriptions: {
    callback: ProximityCallback;
    filter?: EntityFilter;
  }[] = [];
  private readonly lastKnownEntities = new Set<WorldEntityUUID>();

  constructor(
    public readonly entityUuid: WorldEntityUUID,
    private readonly range: number,
    private readonly distanceCalculator: (typeof DISTANCE_CALCULATORS)[keyof typeof DISTANCE_CALCULATORS]
  ) {}

  addSubscription(subscription: {
    callback: ProximityCallback;
    filter?: EntityFilter;
  }) {
    this.subscriptions.push(subscription);
  }

  update(
    trackedEntity: WorldEntityData | undefined,
    allEntities: WorldEntityData[]
  ) {
    if (!trackedEntity) return;

    const currentEntities = new Set<WorldEntityUUID>();

    // Find entities that entered range
    allEntities
      .filter((e) => e.uuid !== this.entityUuid)
      .filter((e) => this.distanceCalculator(trackedEntity, e) <= this.range)
      .forEach((entity) => {
        currentEntities.add(entity.uuid);

        // Only trigger enter callback if entity wasn't in range last frame
        if (!this.lastKnownEntities.has(entity.uuid)) {
          this.subscriptions
            .filter(({ filter }) => !filter || filter(entity))
            .forEach(({ callback }) => {
              const cleanup = callback(entity);
              if (cleanup) {
                this.entitiesInRange.set(entity.uuid, cleanup);
              }
            });
        }
      });

    // Find entities that left range
    [...this.entitiesInRange.entries()].forEach(([uuid, cleanup]) => {
      if (!currentEntities.has(uuid)) {
        cleanup();
        this.entitiesInRange.delete(uuid);
      }
    });

    // Update last known entities
    this.lastKnownEntities.clear();
    currentEntities.forEach((uuid) => this.lastKnownEntities.add(uuid));
  }

  dispose() {
    this.entitiesInRange.forEach((cleanup) => cleanup());
    this.entitiesInRange.clear();
    this.subscriptions.length = 0;
    this.lastKnownEntities.clear();
  }
}

export class EntityProximityTrackingSystem implements EcsSystem {
  private readonly sensors = new Set<ProximitySensor>();

  initialize({ events }: EcsSystemContext) {
    events.onFrame.add((context, _delta) => {
      const entities = context.getEntitiesData();
      this.sensors.forEach((sensor) => {
        const trackedEntity = entities.find(
          (e) => e.uuid === sensor.entityUuid
        );
        sensor.update(trackedEntity, entities);
      });
    });

    events.onEntityRemoved.add((_context, entityData) => {
      console.debug("🧹 Entity removed", entityData.name);

      this.sensors.forEach((sensor) => {
        if (sensor.entityUuid === entityData.uuid) {
          sensor.dispose();
          this.sensors.delete(sensor);
        }
      });
    });
  }

  dispose() {
    console.debug("🧹 Disposing proximity tracking system");
    this.sensors.forEach((sensor) => sensor.dispose());
    this.sensors.clear();
  }

  public getApi(_context: EcsSystemContext) {
    return {
      onOtherEntityEnterRange: (
        entityUuid: WorldEntityUUID,
        range: number,
        metric: DistanceMetric = "manhattan",
        callback: ProximityCallback,
        filter?: EntityFilter
      ) => {
        const sensor = new ProximitySensor(
          entityUuid,
          range,
          DISTANCE_CALCULATORS[metric]
        );
        sensor.addSubscription({ callback, filter });
        this.sensors.add(sensor);

        return () => {
          sensor.dispose();
          this.sensors.delete(sensor);
        };
      },
    };
  }
}
