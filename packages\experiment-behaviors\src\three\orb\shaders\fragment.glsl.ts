import { noiseShader } from "./noise.glsl";

export const fragmentShader = `precision highp float;
${noiseShader}

uniform sampler2D envMapA;
uniform sampler2D envMapB;
uniform float textureBlendFactor;
uniform float time;
uniform float bubbleNoiseScale;
uniform float rippleNoiseScale;
uniform float offsetA;
uniform float offsetB;
uniform float textureDistortionAmount;
uniform float textureDistortionOffset;
varying vec3 vNormal;
varying vec3 vViewPos;
varying vec3 vWorldPos;

// Convert cartesian to spherical coordinates
vec2 cartesianToSpherical(vec3 v) {
  return vec2(
    atan(v.z, v.x) / (2.0 * 3.14159265359) + 0.5,
    asin(v.y) / 3.14159265359 + 0.5
  );
}

// Mirror wrap a value between 0 and 1
float mirrorWrap(float x) {
  float f = fract(x * 0.5); // Get fractional part of x/2
  return f < 0.5 ? f * 2.0 : (1.0 - f) * 2.0; // Mirror the second half
}

// Sample equirectangular texture with proper aspect ratio
vec3 sampleEquirectangular(sampler2D tex, vec2 uv, float offset) {
  // Add distortion based on simplex noise
  float distortionX = snoise(vec3(uv * 4.0, time * 0.5)) * textureDistortionAmount;
  float distortionY = snoise(vec3(uv * 4.0 + 100.0, time * 0.5 + textureDistortionOffset)) * textureDistortionAmount;
  
  // Adjust horizontal sampling to account for equirectangular projection
  float x = mirrorWrap(uv.x * 1.7 - 0.35 + distortionX);
  float y = mirrorWrap(uv.y + offset + distortionY);
  
  // Clamp x to valid range
  x = clamp(x, 0.0, 1.0);
  
  return texture2D(tex, vec2(x, y)).rgb;
}

void main(){
  vec3 N = normalize(vNormal);
  vec3 V = normalize(vViewPos);
  // reflect vector
  vec3 R = reflect(-V, N);
  
  // Fixed rotation for consistent reflection
  mat4 fixedRotation = mat4(
    0.0, 0.0, -1.0, 0.0,
    0.0, 1.0, 0.0, 0.0,
    1.0, 0.0, 0.0, 0.0,
    0.0, 0.0, 0.0, 1.0
  );
  vec3 rotatedR = (fixedRotation * vec4(R, 0.0)).xyz;
  
  // Convert reflection vector to UV coordinates
  vec2 uv = cartesianToSpherical(rotatedR);
  
  // Sample textures with proper aspect ratio
  vec3 envColorA = sampleEquirectangular(envMapA, uv, offsetA);
  vec3 envColorB = sampleEquirectangular(envMapB, uv, offsetB);
  vec3 envColor = mix(envColorA, envColorB, textureBlendFactor);
  
  // subtle fresnel for edge glow
  float fresnel = pow(1.0 - dot(N, V), 3.0);
  vec3 pinkRim = vec3(0.7, 0.4, 0.9); // Soft pink color
  vec3 baseCol = mix(envColor, pinkRim, fresnel * 0.4);
  gl_FragColor = vec4(baseCol, 1.0);
}`;
