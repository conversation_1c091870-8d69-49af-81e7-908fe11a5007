name: Deploy Firestore Rules and Indexes
on:
  workflow_dispatch: {}
  push:
    branches:
      - main
    paths:
      - .github/workflows/firebase-deploy-rules.yml
      - firestore.rules
      - firestore.indexes.json

concurrency:
  group: firebase-rules
  cancel-in-progress: true

jobs:
  deploy_rules_and_indexes:
    runs-on: ubuntu-latest
    permissions:
      contents: "read"
      id-token: "write"
    steps:
      - uses: actions/checkout@v4
      - uses: google-github-actions/auth@v2
        with:
          # Note: these are not secrets
          service_account: "<EMAIL>"
          workload_identity_provider: "projects/************/locations/global/workloadIdentityPools/github/providers/nilo-technologies-org"
      - uses: pnpm/action-setup@v4
        with:
          version: 10.14.0
      - uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: pnpm
      - run: pnpm install
      - run: |
          pnpm exec firebase deploy --non-interactive --force --only firestore || (sleep 30 && pnpm exec firebase deploy --non-interactive --force --only firestore)
