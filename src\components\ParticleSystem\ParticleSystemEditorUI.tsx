import React, { useEffect, useRef, useState } from "react";
import { Vector3 } from "three";
import { SingleSlider } from "@/components/common/SingleSlider";
import { Vector3Slider } from "@/components/common/Vector3Slider";
import { Vector3RangeInput } from "@/components/common/Vector3RangeInput";
import { RangeInput } from "@/components/common/RangeInput";
import { Option, OptionContainer } from "@/components/WorldSettings/Option";
import styles from "@/components/WorldSettings/WorldSettings.module.css";
import { ParticleEntity } from "@/core/entity/ParticleEntity";
import {
  DEFAULT_PARTICLE_SETTINGS,
  ParticleSystemSettings,
} from "@/core/particles/ParticleSystem";

import { SelectionBoxManager } from "@/core/util/ui/SelectionBoxManager";

interface SavedParticlePreset extends ParticleSystemSettings {
  name: string;
}

const PRESET_TEXTURES: Record<string, string> = {
  "Forest Leaves": "/textures/leaf.png",
} as const;

export function ParticleSystemEditorUI({
  particleEntities,
}: {
  particleEntities: ParticleEntity[];
}) {
  const firstParticleEntity = particleEntities[0];
  const [isEnabled, setIsEnabled] = useState(() => {
    return firstParticleEntity.settings.visible;
  });

  const [customPresets, setCustomPresets] = useState<SavedParticlePreset[]>([]);
  const [settings, setSettings] = useState<ParticleSystemSettings>(() => {
    return firstParticleEntity.settings;
  });

  const fileInputRef = useRef<HTMLInputElement>(null);
  const textureInputRef = useRef<HTMLInputElement>(null);

  // Add a listener for particle system visibility changes
  useEffect(() => {
    const handleSettingsUpdate = (updatedSettings: ParticleSystemSettings) => {
      // Only update if the values are actually different to prevent loops
      if (updatedSettings.visible !== isEnabled) {
        setIsEnabled(updatedSettings.visible);
      }

      // Use a deep comparison to avoid unnecessary updates
      const hasSettingsChanged =
        JSON.stringify(updatedSettings) !== JSON.stringify(settings);
      if (hasSettingsChanged) {
        setSettings(updatedSettings);
      }
    };

    // Subscribe to particle system updates
    const unsubscribe =
      firstParticleEntity.particleSystem.onUpdateOptions.on(
        handleSettingsUpdate
      );

    // Update particle system when settings change
    const newSettings = {
      ...settings,
      visible: isEnabled,
    };

    // Only update if the current particle system settings are different
    const currentSettings = firstParticleEntity.getSettings();
    const hasChanged =
      JSON.stringify(newSettings) !== JSON.stringify(currentSettings);

    if (hasChanged) {
      firstParticleEntity.updateSettings(newSettings);
    }

    return () => {
      unsubscribe();
    };
  }, [isEnabled, settings, firstParticleEntity]);

  // Clean up selection box when component unmounts
  useEffect(() => {
    // Show selection box initially with current values
    const { region } = settings;

    // Set a nice color for the selection box
    SelectionBoxManager.setColor(0x00ffaa);
    SelectionBoxManager.setOpacity(0.7);

    // Show the box
    SelectionBoxManager.show(
      new Vector3(region.x.min, region.y.min, region.z.min),
      new Vector3(region.x.max, region.y.max, region.z.max),
      firstParticleEntity.getRootNode().matrixWorld
    );

    return () => {
      // Ensure selection box is removed when component unmounts
      SelectionBoxManager.kill();
    };
  }, [settings, firstParticleEntity]);

  const handleTextureChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedPreset = customPresets.find(
      (preset) => preset.name === event.target.value
    );
    if (selectedPreset) {
      setSettings((_prev) => ({
        ...selectedPreset,
      }));
    } else if (PRESET_TEXTURES[event.target.value]) {
      setSettings((prev) => ({
        ...prev,
        texturePath: PRESET_TEXTURES[event.target.value],
      }));
    }
  };

  const handleCustomTextureUpload = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result;
        if (result && typeof result === "string") {
          // Only update local state - the useEffect will handle entity updates
          setSettings((prev) => ({
            ...prev,
            texturePath: result,
          }));
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const exportSettings = () => {
    const name = prompt(
      //
      "Enter a name for this preset:",
      "My Preset"
    );
    if (!name) return;

    const preset: SavedParticlePreset = { ...settings, name };

    const settingsJson = JSON.stringify(preset, null, 2);
    const blob = new Blob([settingsJson], { type: "application/json" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `${name.toLowerCase().replace(/\s+/g, "-")}-particle-settings.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const importSettings = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const parsed = JSON.parse(
            e.target?.result as string
          ) as SavedParticlePreset;
          setCustomPresets((prev) => [...prev, parsed]);
          setSettings({
            ...DEFAULT_PARTICLE_SETTINGS,
            ...parsed,
          });
        } catch (error) {
          console.error("Failed to parse settings file:", error);
        }
      };
      reader.readAsText(file);
    }
  };

  const handleRangeSliderChange = (axis: string, min: number, max: number) => {
    // Create updated region with the new values
    const newRegion = {
      ...settings.region,
      [axis]: { min, max },
    };

    // Update settings
    setSettings((prev) => ({
      ...prev,
      region: newRegion,
    }));

    // Update selection box with the new region values
    SelectionBoxManager.updateSize(
      new Vector3(newRegion.x.min, newRegion.y.min, newRegion.z.min),
      new Vector3(newRegion.x.max, newRegion.y.max, newRegion.z.max),
      firstParticleEntity.getRootNode().matrixWorld
    );
  };

  // Add color picker handler
  const handleColorChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newColor = event.target.value;
    setSettings((prev) => ({
      ...prev,
      color: newColor,
    }));
  };

  return (
    <div
      className={styles.section}
      style={{
        maxHeight: "500px",
        overflow: "auto",
      }}
    >
      <div className={styles.block}>
        <p className={styles.blockTitle}>Particle System</p>
        <OptionContainer>
          <Option
            label="off"
            active={!isEnabled}
            onClick={() => setIsEnabled(false)}
          />
          <Option
            label="on"
            active={isEnabled}
            onClick={() => setIsEnabled(true)}
          />
        </OptionContainer>
      </div>

      {isEnabled && (
        <>
          <h4>Particle Presets</h4>
          <div className={styles.row}>
            <select
              id="texture-select"
              value={
                Object.entries(PRESET_TEXTURES).find(
                  ([_, path]) => path === settings.texturePath
                )?.[0] ||
                customPresets.find(
                  (preset) => preset.texturePath === settings.texturePath
                )?.name ||
                ""
              }
              onChange={handleTextureChange}
              title="Select particle preset"
              aria-label="Select particle preset"
              className={styles.presetSelect}
            >
              <optgroup label="Default">
                {Object.entries(PRESET_TEXTURES).map(([name, _]) => (
                  <option key={name} value={name}>
                    {name}
                  </option>
                ))}
              </optgroup>
              {customPresets.length > 0 && (
                <optgroup label="Custom">
                  {customPresets.map((preset) => (
                    <option key={preset.name} value={preset.name}>
                      {preset.name}
                    </option>
                  ))}
                </optgroup>
              )}
            </select>
          </div>

          <div className={styles.buttonRow}>
            <button onClick={exportSettings} className={styles.actionButton}>
              Export
            </button>
            <button
              onClick={() => fileInputRef.current?.click()}
              className={styles.actionButton}
            >
              Import
            </button>
            <button
              onClick={() => textureInputRef.current?.click()}
              className={styles.actionButton}
            >
              Upload Texture
            </button>
          </div>
          <input
            ref={fileInputRef}
            type="file"
            accept=".json"
            onChange={importSettings}
            style={{ display: "none" }}
            title="Import particle settings"
            aria-label="Import particle settings"
          />
          <input
            ref={textureInputRef}
            type="file"
            accept="image/*"
            onChange={handleCustomTextureUpload}
            style={{ display: "none" }}
            title="Upload custom texture"
            aria-label="Upload custom texture"
          />

          <h4>Particle Properties</h4>
          <SingleSlider
            label="Particle Count"
            value={settings.count}
            onChange={(value) =>
              setSettings((prev) => ({ ...prev, count: value }))
            }
            min={0}
            max={1024}
            step={1}
          />

          {/* Add color picker */}
          <div className={styles.row}>
            <h4>Particle Color</h4>
            <div className={styles.colorPickerContainer}>
              <input
                id="particle-color"
                type="color"
                value={settings.color || "#ffffff"}
                onChange={handleColorChange}
                className={styles.colorPicker}
                title="Select particle color"
                aria-label="Select particle color"
              />
              <span className={styles.colorValue}>
                {settings.color || "#ffffff"}
              </span>
            </div>
          </div>

          <Vector3Slider
            label="Base Velocity"
            value={settings.baseVelocity}
            onChange={(value) =>
              setSettings((prev) => ({ ...prev, baseVelocity: value }))
            }
            min={-10}
            max={10}
            step={0.01}
          />

          <div className={styles.row}>
            <button
              onClick={() => {
                setSettings((prev) => ({
                  ...prev,
                  baseVelocity: { x: 0, y: 0, z: 0 },
                  velocityVariation: { x: 0, y: 0, z: 0 },
                }));
              }}
              className={styles.actionButton}
              style={{ padding: "2px 8px", fontSize: "0.8em" }}
              title="Reset velocity settings to defaults"
            >
              ↺ Reset Velocity
            </button>
          </div>

          <Vector3Slider
            label="Velocity Variation"
            value={settings.velocityVariation}
            onChange={(value) =>
              setSettings((prev) => ({ ...prev, velocityVariation: value }))
            }
            min={0}
            max={1}
            step={0.01}
          />

          <Vector3RangeInput
            label="Spawn Region"
            value={settings.region}
            onChange={handleRangeSliderChange}
            min={-10}
            max={10}
            step={0.1}
          />

          <RangeInput
            label="Lifetime"
            min={1}
            max={60}
            minValue={settings.lifetime.min}
            maxValue={settings.lifetime.max}
            step={1}
            onChange={(min, max) =>
              setSettings((prev) => ({
                ...prev,
                lifetime: { min, max },
              }))
            }
          />

          <RangeInput
            label="Scale"
            min={0.1}
            max={10}
            minValue={settings.scale.min}
            maxValue={settings.scale.max}
            step={0.1}
            onChange={(min, max) =>
              setSettings((prev) => ({
                ...prev,
                scale: { min, max },
              }))
            }
          />

          <SingleSlider
            label="Opacity"
            value={settings.opacity}
            onChange={(value) =>
              setSettings((prev) => ({ ...prev, opacity: value }))
            }
            min={0}
            max={1}
            step={0.01}
          />

          <div className={styles.block}>
            <p className={styles.blockTitle}>Billboard Mode</p>
            <OptionContainer>
              <Option
                label="off"
                active={!settings.billboard}
                onClick={() =>
                  setSettings((prev) => ({ ...prev, billboard: false }))
                }
              />
              <Option
                label="on"
                active={settings.billboard}
                onClick={() =>
                  setSettings((prev) => ({ ...prev, billboard: true }))
                }
              />
            </OptionContainer>
          </div>
        </>
      )}
    </div>
  );
}
