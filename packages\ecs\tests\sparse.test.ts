import { test, expect } from "@jest/globals";
import { World } from "../src/world";
import { Query, QueryPattern } from "../src/query";
import { System, SystemContext, SystemRunner } from "../src/system";
import { EntityData } from "../src/entityData";
import { EntityId } from "../src/entity";
import {
  OtherMarker,
  Position,
  SparseMarker,
  Vector2,
  Velocity,
} from "./common";
import { Name } from "@nilo/ecs";

test("Sparse components", () => {
  const world = new World();

  const tick = world.archetypeTick;
  world.addEntity([[Position, { x: 1, y: 1 }]]);
  expect(world.archetypeTick).toBe(tick + 1);

  const entity = world.addEntity([[Position, { x: 0, y: 0 }]]);
  expect(world.archetypeTick).toBe(tick + 1);

  world.addComponents(entity, [[SparseMarker, "test"]]);
  expect(world.archetypeTick).toBe(tick + 1); // Sparse components do not trigger arch ticks

  expect(world.getComponent(entity, SparseMarker)).toEqual("test");
  expect(world.entityData(entity)?.getComponent(SparseMarker)).toEqual("test");
});

test("Sparse components in query", () => {
  const world = new World();
  const entity = world.addEntity([[Position, { x: 0, y: 0 }]]);
  const entity2 = world.addEntity([[Position, { x: 1, y: 1 }]]);

  world.addComponents(entity, [[SparseMarker, "test"]]);

  const query = new Query({ Position, SparseMarker });

  const foundData: { Position: Vector2; SparseMarker: string }[] = [];
  query.forEach(world, (_entity, data) => {
    foundData.push(data);
  });

  const entityLoc = world.getLocation(entity);
  const entityLoc2 = world.getLocation(entity2);

  expect(entityLoc.archetype).toBe(entityLoc2.archetype);

  const sparseMarker = entityLoc.archetype.getSparseComponent(
    entityLoc.index,
    SparseMarker
  );
  expect(sparseMarker).toBe("test");

  const sparseMarker2 = entityLoc2.archetype.getSparseComponent(
    entityLoc2.index,
    SparseMarker
  );
  expect(sparseMarker2).toBeNull();

  expect(foundData).toEqual([
    {
      Position: { x: 0, y: 0 },
      SparseMarker: "test",
    },
  ]);
});

test("Sparse components in system", () => {
  const world = new World();
  const system = new SparseObserverSystem();
  const systemRunner = new SystemRunner([system]);
  world.registerSystemRunner(systemRunner);

  const entity = world.addEntity([[Position, { x: 0, y: 0 }]]);

  expect(system.addedEntities).toEqual([]);

  world.addComponents(entity, [[SparseMarker, "test2"]]);

  expect(world.hasComponent(entity, SparseMarker)).toBe(true);
  expect(system.addedEntities).toEqual([]);
  world.addComponents(entity, [[OtherMarker, "test2"]]);

  expect(system.addedEntities).toEqual([entity]);

  world.removeComponent(entity, SparseMarker);

  expect(system.removedEntities).toEqual([entity]);

  // removing again should not trigger
  world.removeComponent(entity, SparseMarker);

  expect(system.removedEntities).toEqual([entity]);

  world.removeComponent(entity, OtherMarker);

  // Wasn't matching before, so should not trigger
  expect(system.removedEntities).toEqual([entity]);

  world.addComponents(entity, [[SparseMarker, "test3"]]);
  expect(system.addedEntities).toEqual([entity]);

  world.addComponents(entity, [[OtherMarker, ""]]);
  world.addComponents(entity, [[OtherMarker, "other"]]);

  expect(system.addedEntities).toEqual([entity, entity]);
  expect(world.getComponent(entity, OtherMarker)).toEqual("other");
  world.removeEntity(entity);

  expect(system.removedEntities).toEqual([entity, entity]);
});

class SparseObserverSystem implements System {
  entityFilter: QueryPattern = [Position, SparseMarker, OtherMarker];
  name: string = "SparseObserverSystem";
  addedEntities: EntityId[] = [];
  removedEntities: EntityId[] = [];

  onEntityAdded(
    _world: World,
    entity: EntityData,
    _systemContext: SystemContext
  ): void {
    this.addedEntities.push(entity.id());
  }
  onEntityRemoved?(
    _world: World,
    entity: EntityData,
    _systemContext: SystemContext
  ): void {
    this.removedEntities.push(entity.id());
  }
}

test("Sparse removal", () => {
  const world = new World();

  const entity1 = world.addEntity([
    [Position, { x: 0, y: 0 }],
    [Velocity, { x: 1, y: 1 }],
    [Name, "entity1"],
  ]);

  const entity2 = world.addEntity([
    [Position, { x: 1, y: 1 }],
    [Velocity, { x: 1, y: 1 }],
    [Name, "entity2"],
  ]);

  const query = new Query({
    pos: Position,
    sparse: SparseMarker,
    name: Name,
  }).trackModified();

  function extractData(): string[] {
    const foundData: string[] = [];
    query.forEach(world, (_entity, data) => {
      foundData.push(data.name);
    });

    return foundData;
  }

  expect(extractData()).toEqual([]);

  world.addComponents(entity1, [[SparseMarker, "test1"]]);
  world.addComponents(entity2, [[SparseMarker, "test2"]]);

  expect(extractData()).toEqual(["entity1", "entity2"]);
  expect(extractData()).toEqual([]);

  expect(world.getComponent(entity1, SparseMarker)).toEqual("test1");
  expect(world.getComponent(entity2, SparseMarker)).toEqual("test2");

  world.addComponents(entity1, [[SparseMarker, "test1"]]);
  world.removeComponent(entity1, Velocity);
  expect(extractData()).toEqual(["entity1"]);

  expect(world.getComponent(entity1, SparseMarker)).toEqual("test1");
  expect(world.getComponent(entity2, SparseMarker)).toEqual("test2");
});
