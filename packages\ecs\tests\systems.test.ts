import { expect, test } from "@jest/globals";
import { System, SystemContext, SystemRunner } from "../src/system";
import { World } from "../src/world";
import { QueryPattern } from "../src/query";
import { EntityId } from "../src/entity";
import { EntityData } from "../src/entityData";
import { Position, Velocity } from "./common";

export class TestSystem implements System {
  updateCount: number = 0;
  name: string = "TestSystem";

  run(_world: World, systemContext: SystemContext): void {
    systemContext.debug("Update count:", this.updateCount);
    this.updateCount++;
  }
}

export class ObserverSystem implements System {
  addedEntities: EntityId[] = [];
  removedEntities: EntityId[] = [];
  name: string = "ObserverSystem";

  entityFilter?: QueryPattern | undefined = [Position];

  onEntityAdded(
    _world: World,
    entity: EntityData,
    _systemContext: SystemContext
  ): void {
    this.addedEntities.push(entity.entity);
  }

  onEntityRemoved(
    _world: World,
    entity: EntityData,
    _systemContext: SystemContext
  ): void {
    this.removedEntities.push(entity.entity);
  }
}

test("Basic update system", () => {
  const world = new World();
  const testSystem = new TestSystem();
  const systemRunner = new SystemRunner([testSystem]);
  world.registerSystemRunner(systemRunner);

  world.addEntity();

  systemRunner.run(world, 0.016);

  expect(testSystem.updateCount).toBe(1);

  systemRunner.run(world, 0.016);

  expect(testSystem.updateCount).toBe(2);
});

test("Observer system", () => {
  const world = new World();
  const observerSystem = new ObserverSystem();
  const systemRunner = new SystemRunner([observerSystem]);
  world.registerSystemRunner(systemRunner);

  const entity = world.addEntity();

  systemRunner.run(world, 0.016);

  expect(observerSystem.addedEntities).toEqual([]); // not matching yet

  world.addComponents(entity, [[Position, { x: 0, y: 0 }]]);

  systemRunner.run(world, 0.016);

  expect(observerSystem.addedEntities).toEqual([entity]);

  world.addComponents(entity, [[Velocity, { x: 0, y: 0 }]]);

  expect(observerSystem.addedEntities).toEqual([entity]); // Not triggered twice

  world.removeComponent(entity, Position);

  systemRunner.run(world, 0.016);

  expect(observerSystem.removedEntities).toEqual([entity]);
});
