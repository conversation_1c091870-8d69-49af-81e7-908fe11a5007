import { textureLoadingService } from "@nilo/experiment-behaviors/services/TextureLoadingService";
import type { EcsSystemContext } from "@nilo/experiment-behaviors/system-context/createSystemContext";
import { ThreejsContainersSystem } from "@nilo/experiment-behaviors/systems/ThreejsContainersSystem";
import { createGenerationOrb } from "@nilo/experiment-behaviors/three/orb/createGenerationOrb";
import { createTextureCycler } from "@nilo/experiment-behaviors/three/orb/createTextureCycler";
import {
  type BuiltInComponentData,
  BuiltInComponentTypes,
} from "@nilo/experiment-behaviors/types/BuiltInComponentData";
import type { EcsSystem } from "@nilo/experiment-behaviors/types/EcsSystem";
import type { WorldEntityUUID } from "@nilo/experiment-behaviors/types/NilusEntityData";
import { createMulticaster } from "@nilo/experiment-behaviors/utils/createMulticaster";

const CHILD_KEY = "generation-orb";

export class DisplayGenerationOrbsSystem implements EcsSystem {
  private readonly orbControllers = new Map<WorldEntityUUID, OrbController>();

  private lastFrameTime = performance.now();
  private animationFrameId?: number;

  private readonly onDispose = createMulticaster();

  initialize({ events }: EcsSystemContext) {
    events.onEntityComponentAddedByType.add(
      BuiltInComponentTypes.generationBlob,
      (context, entityData, componentData) => {
        const existingCtrl = this.orbControllers.get(entityData.uuid);
        if (existingCtrl) {
          console.warn(
            "🔶 Entity already has a generation blob, skipping:",
            entityData.uuid
          );
          return;
        }

        const orb = new OrbController(componentData);
        this.orbControllers.set(entityData.uuid, orb);

        const containersApi = //
          context.getRequiredSystemApiByConstructor(ThreejsContainersSystem);
        containersApi.addThreejsChildToEntity(
          entityData.uuid,
          orb.orb.mesh,
          CHILD_KEY
        );

        console.debug("🔶 Generation blob added:", entityData.uuid);
      }
    );

    events.onEntityComponentRemovedByType.add(
      BuiltInComponentTypes.generationBlob,
      (_context, entityData) => {
        const existingCtrl = this.orbControllers.get(entityData.uuid);
        if (existingCtrl) {
          existingCtrl.orb.stopTimeLoop();
          existingCtrl.orb.mesh.clear();
          this.orbControllers.delete(entityData.uuid);
        }
      }
    );

    events.onEntityComponentUpdatedByType.add(
      BuiltInComponentTypes.generationBlob,
      (context, entityData, componentData) => {
        const ctrl = this.orbControllers.get(entityData.uuid);
        if (!ctrl) {
          console.warn(
            "🔶 Entity has no generation blob, skipping:",
            entityData.uuid
          );
          return;
        }

        ctrl.onComponentParamsChange(componentData.params);
      }
    );

    this.startAnimationLoop();
  }

  private startAnimationLoop() {
    const animate = () => {
      const currentTime = performance.now();
      const deltaTime = (currentTime - this.lastFrameTime) / 1000;
      this.lastFrameTime = currentTime;

      for (const ctrl of this.orbControllers.values()) {
        ctrl.onEnterFrame(deltaTime);
      }

      this.animationFrameId = requestAnimationFrame(animate);
    };

    animate();
  }

  dispose() {
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
    }

    this.orbControllers.forEach((ctrl) => ctrl.dispose());
    this.orbControllers.clear();
    this.onDispose.invoke();
  }
}

const assetUrlBase = "https://assets.public.cx/work-nilo/img-lib/";
const textureUrls = Array.from(
  { length: 34 },
  (_, i) => `${assetUrlBase}/suggestion-${String(i + 1).padStart(2, "0")}.webp`
);
const shuffle = (array: string[]) => {
  return array.sort(() => Math.random() - 0.5);
};
const placeholderPreviewTextureUrls = shuffle([...textureUrls]);
class OrbController {
  private readonly textureCycler = createTextureCycler(
    placeholderPreviewTextureUrls
  );

  public readonly orb: ReturnType<typeof createGenerationOrb>;

  constructor(
    private readonly componentData: BuiltInComponentData<
      typeof BuiltInComponentTypes.generationBlob
    >
  ) {
    const { url1, url2 } = this.textureCycler.onFrame(0);
    const textureA = textureLoadingService.getOrLoadTexture(url1);
    const textureB = textureLoadingService.getOrLoadTexture(url2);

    const orb = createGenerationOrb({
      radius: 0.5,
      envMapA: textureA,
      envMapB: textureB,
      textureBlendFactor: 0,
      bubbleNoiseScale: componentData.params.isGenerating ? 0.5 : 0.3,
      rippleNoiseScale: componentData.params.isGenerating ? 1.5 : 0.2,
      timeScale: componentData.params.isGenerating ? 1 : 0.2,
    });

    orb.startTimeLoop();

    this.orb = orb;
  }

  onComponentParamsChange(
    updates: Partial<
      BuiltInComponentData<
        typeof BuiltInComponentTypes.generationBlob
      >["params"]
    >
  ) {
    const { isGenerating = false, previewImageUri } = updates;

    if (previewImageUri) {
      const texture = textureLoadingService.getOrLoadTexture(previewImageUri);
      this.orb.updateProps({
        envMapA: texture,
        envMapB: texture,
        textureBlendFactor: 0,
        textureDistortionAmount: isGenerating ? 0.01 : 0,
        textureDistortionTimeScale: isGenerating ? 4 : 2,
        ...makeNoiseProps(isGenerating),
      });
    } else {
      const { blendFactor, url1, url2 } = this.textureCycler.onFrame(0);
      const zeroToOneToZero =
        2 * (0.5 - Math.abs(0.5 - smoothstep(blendFactor)));

      this.orb.updateProps({
        envMapA: textureLoadingService.getOrLoadTexture(url1),
        envMapB: textureLoadingService.getOrLoadTexture(url2),
        textureBlendFactor: smoothstep(blendFactor),
        textureDistortionAmount: zeroToOneToZero * (isGenerating ? 0.1 : 0.01),
        textureDistortionTimeScale: isGenerating ? 4 : 1,
        ...makeNoiseProps(isGenerating),
      });
    }

    Object.assign(this.componentData, updates);
  }

  onEnterFrame(deltaTime: number) {
    const { isGenerating, previewImageUri } = this.componentData.params;

    if (!previewImageUri) {
      const { blendFactor, url1, url2 } = this.textureCycler.onFrame(deltaTime);
      const zeroToOneToZero =
        2 * (0.5 - Math.abs(0.5 - smoothstep(blendFactor)));

      this.orb.updateProps({
        envMapA: textureLoadingService.getOrLoadTexture(url1),
        envMapB: textureLoadingService.getOrLoadTexture(url2),
        textureBlendFactor: smoothstep(blendFactor),
        textureDistortionAmount: zeroToOneToZero * (isGenerating ? 0.1 : 0.01),
        textureDistortionTimeScale: isGenerating ? 4 : 1,
        ...makeNoiseProps(isGenerating),
      });
    }
  }

  dispose() {
    this.orb.stopTimeLoop();
    this.orb.mesh.clear();
  }
}

const smoothstep = (x: number) => {
  x = Math.max(0, Math.min(1, x));
  return x * x * (3 - 2 * x);
};

const makeNoiseProps = (isGenerating: boolean) => ({
  bubbleNoiseScale: isGenerating ? 0.5 : 0.3,
  rippleNoiseScale: isGenerating ? 1.5 : 0.2,
  timeScale: isGenerating ? 1 : 0.2,
});
