import { expect, test } from "@jest/globals";
import { World } from "../src/world";
import { ComponentKey } from "../src/archetype";
import { SparseMarker, Position } from "./common";

test("Explicit component registration handler", () => {
  const world = new World();
  const components: ComponentKey[] = [];
  world.onRegisterComponent((c) => components.push(c.KEY));
  world.registerComponent(Position);
  expect(components).toStrictEqual([Position.KEY]);
});

test("Call component registration handlers on already-known components", () => {
  const world = new World();
  const components: ComponentKey[] = [];
  world.registerComponent(Position);
  world.onRegisterComponent((c) => components.push(c.KEY));
  expect(components).toStrictEqual([Position.KEY]);
});

test("Register component when added", () => {
  const world = new World();
  const components: ComponentKey[] = [];
  const entity = world.addEntity();
  world.addComponent(entity, Position, { x: 0, y: 0 });
  world.onRegisterComponent((c) => components.push(c.KEY));
  expect(components).toStrictEqual([Position.KEY]);
});

test("Register sparse component when added", () => {
  const world = new World();
  const components: ComponentKey[] = [];
  const entity = world.addEntity();
  world.addComponent(entity, SparseMarker, "so sparse");
  world.onRegisterComponent((c) => components.push(c.KEY));
  expect(components).toStrictEqual([SparseMarker.KEY]);
});
