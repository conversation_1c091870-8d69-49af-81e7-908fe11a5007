import { AnimationMixer, Object3D } from "three";

import { animationCacheService } from "@nilo/experiment-behaviors/services/AnimationCacheService";
import { DisplayModelsFromUriSystem } from "@nilo/experiment-behaviors/systems/DisplayModelsFromUriSystem";
import { BuiltInComponentTypes } from "@nilo/experiment-behaviors/types/BuiltInComponentData";
import { createMulticaster } from "@nilo/experiment-behaviors/utils/createMulticaster";

import type { EcsSystemContext } from "@nilo/experiment-behaviors/system-context/createSystemContext";
import type { BuiltInComponentData } from "@nilo/experiment-behaviors/types/BuiltInComponentData";
import type { EcsSystem } from "@nilo/experiment-behaviors/types/EcsSystem";
import type { WorldEntityUUID } from "@nilo/experiment-behaviors/types/NilusEntityData";

export class AnimationLoopSystem implements EcsSystem {
  private readonly controllers = new Map<
    WorldEntityUUID,
    AnimationController
  >();
  private readonly pendingModels = new Map<WorldEntityUUID, Object3D>();
  private readonly onDispose = createMulticaster();

  initialize(context: EcsSystemContext) {
    const { events } = context;

    const modelSystemApi = //
      context.getRequiredSystemApiByConstructor(DisplayModelsFromUriSystem);

    // Handle model loading
    modelSystemApi.events.onModelLoaded.add(
      (event: { entityUuid: WorldEntityUUID; modelObject: Object3D }) => {
        const { entityUuid, modelObject } = event;
        const controller = this.controllers.get(entityUuid);
        if (controller) {
          // We have a component, apply animation
          controller.applyToModel(modelObject);
        } else {
          // No component yet, store model for when component is added
          this.pendingModels.set(entityUuid, modelObject);
        }
      }
    );

    // Handle component lifecycle
    events.onEntityComponentAddedByType.add(
      BuiltInComponentTypes.animationLoop,
      (_context, entityData, componentData) => {
        const controller = new AnimationController(
          entityData.uuid,
          componentData
        );

        this.controllers.set(entityData.uuid, controller);

        // If we have a pending model, apply the animation
        const pendingModel = this.pendingModels.get(entityData.uuid);
        if (pendingModel) {
          controller.applyToModel(pendingModel);
          this.pendingModels.delete(entityData.uuid);
        }
      }
    );

    events.onEntityComponentRemovedByType.add(
      BuiltInComponentTypes.animationLoop,
      (_context, entityData) => {
        const controller = this.controllers.get(entityData.uuid);
        if (controller) {
          controller.dispose();
          this.controllers.delete(entityData.uuid);
          console.debug(`🎭 Stopped animation for entity ${entityData.uuid}`);
        }
      }
    );

    events.onEntityComponentUpdatedByType.add(
      BuiltInComponentTypes.animationLoop,
      (_context, entityData, componentData) => {
        const controller = this.controllers.get(entityData.uuid);
        if (!controller) {
          console.warn(
            "🔶 Entity has no animation controller, skipping:",
            entityData.uuid
          );
          return;
        }

        controller.onComponentParamsChange(componentData.params);

        // Reapply to model if it exists
        const model = modelSystemApi.getLoadedModel(entityData.uuid);
        if (model) {
          controller.applyToModel(model);
        }
      }
    );

    events.onFrame.add((_context, delta) => {
      for (const controller of this.controllers.values()) {
        controller.update(delta);
      }
    });
  }

  dispose() {
    for (const controller of this.controllers.values()) {
      controller.dispose();
    }
    this.controllers.clear();
    this.pendingModels.clear();
    this.onDispose.invoke();
  }
}

class AnimationController {
  private mixer?: AnimationMixer;
  private currentClip?: string;

  constructor(
    private readonly entityUuid: WorldEntityUUID,
    private readonly componentData: BuiltInComponentData<
      typeof BuiltInComponentTypes.animationLoop
    >
  ) {}

  applyToModel(modelObject: Object3D) {
    const clipName = this.componentData.params.clip;
    if (clipName === this.currentClip) return;

    const animations = animationCacheService.getAnimationsForUuid(
      modelObject.uuid
    );
    const clip = animations.find((a) => a.name === clipName);

    if (!clip) {
      console.error(
        `🔴 Animation clip "${clipName}" not found for entity ${this.entityUuid}`
      );
      return;
    }

    // Create or get existing mixer
    if (!this.mixer) {
      this.mixer = new AnimationMixer(modelObject);
    }

    // Stop any existing animation
    this.mixer.stopAllAction();

    // Start new animation
    const action = this.mixer.clipAction(clip);
    action.play();
    this.currentClip = clipName;
    console.debug(
      `🎭 Started animation "${clipName}" for entity ${this.entityUuid}`
    );
  }

  onComponentParamsChange(
    updates: Partial<
      BuiltInComponentData<typeof BuiltInComponentTypes.animationLoop>["params"]
    >
  ) {
    if (updates.clip && updates.clip !== this.currentClip) {
      // We'll need to reapply to model when it's available
      this.currentClip = undefined;
    }
  }

  update(delta: number) {
    if (this.mixer) {
      this.mixer.update(delta);
    }
  }

  dispose() {
    if (this.mixer) {
      this.mixer.stopAllAction();
      this.mixer = undefined;
    }
    this.currentClip = undefined;
  }
}
