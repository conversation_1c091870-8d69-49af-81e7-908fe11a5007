import {
  AmbientLight,
  DirectionalLight,
  GridHelper,
  Object3D,
  Scene,
} from "three";

export function setupSampleStageSetup(scene: Scene) {
  // Grid
  const gridHelper = new GridHelper(100, 100, 0x444444, 0x222222);
  scene.add(gridHelper);

  // Lights
  const ambientLight = new AmbientLight(0x404040, 0.8);
  scene.add(ambientLight);
  addDirectionalLight(scene, 0xffffff, 10, [-5, 5, 0]);
  addDirectionalLight(scene, 0xffffff, 10, [5, 5, 0]);
  addDirectionalLight(scene, 0xffffff, 5, [0, 10, 0]);
}

function addDirectionalLight(
  scene: Scene,
  color: number,
  intensity: number,
  position: [number, number, number]
) {
  const light = new DirectionalLight(color, intensity);
  light.position.set(...position);
  light.target = new Object3D();
  light.target.position.set(0, 1, 0);
  scene.add(light.target);
  scene.add(light);

  // if (urlParams.debugDraw) {
  //   const helper = new DirectionalLightHelper(light, 1, 0xffff00);
  //   scene.add(helper);
  // }

  return light;
}
