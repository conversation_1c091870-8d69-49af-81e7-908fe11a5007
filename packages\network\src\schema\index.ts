import {
  ByteStreamReader,
  ByteStreamWriter,
  f32,
  Schema,
  Struct,
  text,
  u16,
  List,
  u32,
  u8Array,
} from "@evenstar/byteform";
import { QuaternionLike, Vector3Like } from "three";

/** Type alias for network schema entity IDs */
export type EntityId = string;

/** Type alias for component IDs */
export type ComponentId = number;

/** Schema of entity identifiers */
export const entitySchema = text;

/** Schema of component identifiers */
export const componentSchema = u32;

/** 3D vector schema */
export const vec3Schema = new Struct({
  x: f32,
  y: f32,
  z: f32,
});

/** Quaternion schema */
export const quatSchema = new Struct({
  x: f32,
  y: f32,
  z: f32,
  w: f32,
});

/** Complete affine transform */
export interface Transform {
  position: Vector3Like;
  scale: Vector3Like;
  orientation: QuaternionLike;
}

export const transformSchema: Schema<Transform> = new Struct({
  position: vec3Schema,
  scale: vec3Schema,
  orientation: quatSchema,
});

export interface UserData {
  transform: Transform;
  color: number;
  selectedEntities: EntityId[];
  targetEntity: EntityId;
  profile: UserProfileData;
}

/** Each field in this struct can be empty to mark absence */
export interface UserProfileData {
  username: string;
  displayName: string;
  avatarUrl: string;
  bio: string;
}

/** User entity-specific data */
export const userDataSchema: Schema<UserData> = new Struct({
  transform: transformSchema,
  // #rrggbb
  color: u32,
  selectedEntities: new List(text),
  targetEntity: entitySchema,
  profile: new Struct({
    username: text,
    displayName: text,
    avatarUrl: text,
    bio: text,
  }),
});

/** Defines a message with a unique numeric tag and data schema. */
export class MessageSchema<T> {
  public readonly tag: number;
  public readonly schema: Schema<T>;

  static _tagNames = new Array<string>();

  /** Provides read access to the immutable mapping of message tags to schema names */
  public static get tagNames() {
    return MessageSchema._tagNames;
  }

  /**
   * Creates a new message schema with a unique tag.
   * This function ensures each message type has a unique identifier.
   * @param name Human-readable name for debugging
   * @param schema The data schema for this message type
   */
  private constructor(name: string, schema: Schema<T>) {
    // not concurrency safe. this is why the constructor is private.
    this.tag = MessageSchema._tagNames.length;
    MessageSchema._tagNames.push(name);
    this.schema = schema;
    console.debug("🔌 Constructed message schema", this.tag, name);
  }

  /** Serialize a message of this schema.
   * @param encoder An external ByteStreamWriter to reuse.
   * @param value The message data to be serialized.
   * @returns Serialized message as Uint8Array
   */
  public serialize(encoder: ByteStreamWriter, value: T): Uint8Array {
    encoder.reset();
    encoder.writeSchema(u16, this.tag);
    encoder.writeSchema(this.schema, value);
    return encoder.commit();
  }

  /** Empty message schema for connection testing */
  static readonly Ping: MessageSchema<object> = new MessageSchema(
    "Ping",
    new Struct({})
  );

  /** Sent to users upon a user joining. Contains that user's entity ID */
  static readonly UserJoin: MessageSchema<EntityId> = new MessageSchema(
    "UserJoin",
    entitySchema
  );

  /** Spawn an entity. */
  static readonly SpawnEntity: MessageSchema<EntityId> = new MessageSchema(
    "SpawnEntity",
    entitySchema
  );

  /** Kill an entity. */
  static readonly KillEntity: MessageSchema<EntityId> = new MessageSchema(
    "KillEntity",
    entitySchema
  );

  /** Insert (create or update) a component onto an entity. */
  static readonly InsertComponent = new MessageSchema(
    "InsertComponent",
    new Struct({
      entity: entitySchema,
      component: componentSchema,
      data: u8Array,
    })
  );

  /** Remove a component from an entity. */
  static readonly RemoveComponent = new MessageSchema(
    "RemoveComponent",
    new Struct({
      entity: entitySchema,
      component: componentSchema,
    })
  );
}

/**
 * Handler function type for processing messages.
 * @template T The context data type
 * @template M The message data type
 */
export type Handler<T, M> = (data: T, message: M) => void;

/** Internal handler type for raw message processing */
type RawHandler = (message: ByteStreamReader) => void;

/** Utility class to route tagged messages to dedicated methods. Generic over the handling class. */
export class Router<T> {
  // Binds the context of each handler in one place.
  private _data: T;

  // Maps each message's tag to a dedicated handler.
  private _handlers: Map<number, RawHandler> = new Map();

  constructor(data: T) {
    this._data = data;
  }

  /**
   * Registers a handler for a specific message schema.
   * @param schema The message schema to handle
   * @param handler Function to process messages of this type
   * @returns This router instance for chaining
   */
  public addHandler<M>(schema: MessageSchema<M>, handler: Handler<T, M>): this {
    if (this._handlers.has(schema.tag)) {
      console.error(`🔌 Handler for tag ${schema.tag} already added`);
      return this;
    }

    // TODO: warn if handlers are overwritten
    this._handlers.set(schema.tag, (reader) => {
      const message = reader.readSchema(schema.schema);
      handler(this._data, message);
    });

    return this;
  }

  /**
   * Parses a message and invokes the appropriate handler in response.
   * @param data Raw message data as Uint8Array
   */
  public handleData(data: Uint8Array) {
    const reader = new ByteStreamReader(data);
    const tag = reader.readSchema(u16);
    const handler = this._handlers.get(tag);

    if (handler) {
      handler(reader);
    } else {
      const name = MessageSchema.tagNames.at(tag);
      console.error("Unhandled tag", tag, name);
    }
  }
}
