import { ChannelType, ProviderType } from "../../types";
import { NetworkStats } from "./NetworkStats";

export class ChannelStats extends NetworkStats {
  private _channelType: ChannelType;
  private _providerType: ProviderType;

  private _parentStats: NetworkStats;

  constructor(
    channelType: ChannelType,
    providerType: ProviderType,
    parentStats: NetworkStats
  ) {
    super();
    this._channelType = channelType;
    this._providerType = providerType;
    this._parentStats = parentStats;
  }

  public override _onSend(byteLength: number, localByteLength: number): void {
    super._onSend(byteLength, localByteLength);
    this._parentStats._onSend(byteLength, localByteLength);
  }

  public override _onReceive(
    byteLength: number,
    localByteLength: number
  ): void {
    super._onReceive(byteLength, localByteLength);
    this._parentStats._onReceive(byteLength, localByteLength);
  }

  public get channelType(): ChannelType {
    return this._channelType;
  }

  public get providerType(): ProviderType {
    return this._providerType;
  }
}
