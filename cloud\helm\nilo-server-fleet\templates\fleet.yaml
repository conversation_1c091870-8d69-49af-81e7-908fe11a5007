apiVersion: "agones.dev/v1"
kind: Fleet
metadata:
  name: {{ .Release.Name }}
spec:
  replicas: 1
  template:
    # GameServer spec
    spec:
      ports:
        - name: default
          containerPort: 20041
          # "TCPUDP" means that it uses both TCP and UDP, and exposes the same hostPort for both protocols.
          # This will mean that it adds an extra port, and the first port is set to TCP, and second port set to UDP
          protocol: TCPUDP
      health:
        initialDelaySeconds: 10
        periodSeconds: 10
      template:
        # Pod spec
        spec:
          volumes:
            - name: certs-volume
              secret:
                secretName: gs-nilo-io-tls
          containers:
            - image: europe-west4-docker.pkg.dev/nilo-technologies/nilo/nilo-server:{{ .Values.imageTag }}
              imagePullPolicy: Always
              name: nilo-server
              volumeMounts:
                - name: certs-volume
                  readOnly: true
                  mountPath: "/etc/nilo/cert"
              env:
                - name: LOG_TO_CLOUD_LOGGING
                  value: "true"
                - name: TLS_CERT_FILE
                  value: "/etc/nilo/cert/tls.crt"
                - name: TLS_KEY_FILE
                  value: "/etc/nilo/cert/tls.key"
                - name: POPULATE_DNS_URL
                  value: "https://us-central1-nilo-technologies.cloudfunctions.net/{{ .Values.isolatedEnvironment.functions }}-populateDns"
                - name: ISOLATED_ENVIRONMENT_NAME
                  value: "{{ .Values.isolatedEnvironment.name }}"
              livenessProbe:
                httpGet:
                  scheme: HTTPS
                  path: /ping
                  port: 20041
                initialDelaySeconds: 10
                periodSeconds: 10
              # TODO: set proper values (once we know what's needed)
              resources:
                requests:
                  memory: "128Mi"
                  cpu: "50m"
                limits:
                  memory: "256Mi"
                  cpu: "200m"
          dnsPolicy: ClusterFirst
          restartPolicy: Always
