import { expect, test } from "@jest/globals";
import { World } from "../src/world";
import { Name } from "../src/component";
import { Position, Velocity } from "./common";

test("Entity creation", () => {
  const world = new World();
  const entity = world.addEntity();

  world.isAlive(entity);

  world.addComponents(entity, [
    [Position, { x: 0, y: 0 }],
    [Velocity, { x: 0, y: 0 }],
  ]);

  expect(world.getComponent(entity, Position)).toEqual({ x: 0, y: 0 });
  expect(world.getComponent(entity, Velocity)).toEqual({ x: 0, y: 0 });

  world.removeComponent(entity, Position);

  expect(world.getComponent(entity, Position)).toBeNull();

  expect(world.isAlive(entity)).toBe(true);

  world.removeEntity(entity);

  expect(world.isAlive(entity)).toBe(false);
});

test("Entity creation with record", () => {
  const world = new World();
  const entity = world.addEntity([
    [Position, { x: 0, y: 0 }],
    [Velocity, { x: 0, y: 0 }],
  ]);

  expect(world.isAlive(entity)).toBe(true);

  expect(world.getComponent(entity, Position)).toEqual({ x: 0, y: 0 });
  expect(world.getComponent(entity, Velocity)).toEqual({ x: 0, y: 0 });

  world.removeComponent(entity, Position);

  expect(world.getComponent(entity, Position)).toBeNull();

  expect(world.isAlive(entity)).toBe(true);

  world.removeEntity(entity);

  expect(world.isAlive(entity)).toBe(false);
});

test("Multiple entities", () => {
  const world = new World();
  const entity = world.addEntity();
  const entity2 = world.addEntity();

  expect(world.isAlive(entity)).toBe(true);

  world.addComponents(entity, [
    [Position, { x: 0, y: 0 }],
    [Velocity, { x: 0, y: 0 }],
  ]);

  world.addComponents(entity2, [
    [Position, { x: 1, y: 1 }],
    [Velocity, { x: 1, y: 0 }],
  ]);

  expect(world.getComponent(entity, Position)).toEqual({ x: 0, y: 0 });
  expect(world.getComponent(entity, Velocity)).toEqual({ x: 0, y: 0 });

  expect(world.getComponent(entity2, Position)).toEqual({ x: 1, y: 1 });
  expect(world.getComponent(entity2, Velocity)).toEqual({ x: 1, y: 0 });

  world.removeComponent(entity, Position);

  // entity2 is not touched, and still accessible as normal
  expect(world.getComponent(entity2, Position)).toEqual({ x: 1, y: 1 });
  expect(world.getComponent(entity2, Velocity)).toEqual({ x: 1, y: 0 });

  expect(world.getComponent(entity, Position)).toBeNull();

  expect(world.isAlive(entity)).toBe(true);

  world.removeEntity(entity);

  expect(world.isAlive(entity)).toBe(false);
});

test("Add remove", () => {
  const world = new World();
  const entity = world.addEntity();

  world.addComponents(entity, [
    [Position, { x: 0, y: 0 }],
    [Name, "entity"],
  ]);

  world.removeComponent(entity, Position);

  const entity2 = world.addEntity();
  world.addComponents(entity2, [
    [Position, { x: 1, y: 1 }],
    [Name, "entity2"],
  ]);

  world.addComponents(entity, [[Position, { x: 2, y: 2 }]]);

  expect(world.getComponent(entity2, Position)).toEqual({ x: 1, y: 1 });
  expect(world.getComponent(entity2, Name)).toEqual("entity2");
  expect(world.getComponent(entity, Position)).toEqual({ x: 2, y: 2 });
  expect(world.getComponent(entity, Name)).toEqual("entity");

  world.removeComponent(entity2, Position);
  world.removeComponent(entity, Position);

  // Ensure entities are not mixed up

  expect(world.getComponent(entity, Name)).toEqual("entity");
  expect(world.getComponent(entity2, Name)).toEqual("entity2");

  world.addComponents(entity, [[Position, { x: 3, y: 3 }]]);

  expect(world.getComponent(entity, Position)).toEqual({ x: 3, y: 3 });
  expect(world.getComponent(entity, Name)).toEqual("entity");

  world.removeComponent(entity, Name);

  expect(world.getComponent(entity, Name)).toBeNull();
});

test("Add twice", () => {
  const world = new World();
  const entity = world.addEntity();
  const entityData = world.entityData(entity);

  world.addComponents(entity, [[Position, { x: 0, y: 0 }]]);

  world.addComponents(entity, [[Position, { x: 1, y: 1 }]]);

  expect(world.getComponent(entity, Position)).toEqual({ x: 1, y: 1 });

  world.setComponent(entity, Position, { x: 2, y: 2 });

  expect(world.getComponent(entity, Position)).toEqual({ x: 2, y: 2 });

  expect(entityData?.getComponent(Position)).toEqual({ x: 2, y: 2 });
});

class TestService {
  name: string = "TestService";
}

test("Services", () => {
  const world = new World();
  const service = new TestService();
  world.addService(service);
  expect(world.service(TestService).name).toBe("TestService");
});

class SubTestService extends TestService {
  name: string = "SubTestService";
}

test("SubServices", () => {
  const world = new World();
  const service = new SubTestService();
  world.addService(service);
  expect(world.service(TestService).name).toBe("SubTestService");
  expect(world.service(SubTestService).name).toBe("SubTestService");

  expect(world.service(SubTestService)).toBe(world.service(TestService));
});
