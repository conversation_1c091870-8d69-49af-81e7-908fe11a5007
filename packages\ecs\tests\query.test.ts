import { expect, test } from "@jest/globals";
import { World } from "../src/world";
import { Component, Name } from "../src/component";
import { EntityId } from "../src/entity";
import { Color, Position, Vector2, Velocity } from "./common";
import { Query } from "@nilo/ecs";

const Health = new Component<number>("health").withDescription(
  "health of the entity"
);

test("Simple query", () => {
  const world = new World();
  const entity = world.addEntity();
  const entity2 = world.addEntity();
  const entity3 = world.addEntity();

  world.addComponents(entity, [
    [Position, { x: 1, y: 2 }],
    [Velocity, { x: 1, y: 3 }],
    [Name, "entity1"],
  ]);

  world.addComponents(entity2, [
    [Position, { x: 1, y: 4 }],
    [Velocity, { x: 1, y: 3 }],
    [Name, "entity2"],
  ]);

  world.addComponents(entity3, [
    [Position, { x: 8, y: 8 }],
    [Name, "entity3"],
  ]);

  const query = new Query({
    pos: Position,
    name: Name,
  });

  const foundData: {
    entity: EntityId;
    data: { name: string; pos: Vector2 };
  }[] = [];

  query.forEach(world, (entity, data) => {
    foundData.push({ entity: entity.id(), data });
  });

  expect(foundData[0].data).toEqual({
    pos: { x: 1, y: 2 },
    name: "entity1",
  });

  expect(foundData[1].data).toEqual({
    pos: { x: 1, y: 4 },
    name: "entity2",
  });

  expect(foundData[2].data).toEqual({
    pos: { x: 8, y: 8 },
    name: "entity3",
  });

  expect(foundData.length).toBe(3);
});

test("Array pattern query", () => {
  const world = new World();
  const entity = world.addEntity([
    [Position, { x: 1, y: 2 }],
    [Name, "entity1"],
  ]);

  const entity2 = world.addEntity([
    [Position, { x: 1, y: 4 }],
    [Velocity, { x: 1, y: 3 }],
    [Name, "entity2"],
  ]);

  const entity3 = world.addEntity([
    [Position, { x: 8, y: 8 }],
    [Name, "entity3"],
  ]);

  const pattern: [Component<Vector2>, Component<string>] = [Position, Name];
  const query = new Query(pattern);

  const foundData: {
    entity: EntityId;
    data: [Vector2, string];
  }[] = [];

  query.forEach(world, (entity, data) => {
    foundData.push({ entity: entity.id(), data });
  });

  expect(foundData).toEqual([
    { entity: entity, data: [{ x: 1, y: 2 }, "entity1"] },
    { entity: entity3, data: [{ x: 8, y: 8 }, "entity3"] },
    { entity: entity2, data: [{ x: 1, y: 4 }, "entity2"] },
  ]);
});

test("Entity creation during query", () => {
  const world = new World();

  world.addEntity([
    [Position, { x: 1, y: 2 }],
    [Name, "entity1"],
  ]);

  const query = new Query({ pos: Position });

  const foundData: { entity: EntityId; data: { pos: Vector2 } }[] = [];

  let entity2: EntityId | null = null;
  query.forEach(world, (entity, data) => {
    // deferred
    entity2 = world.addEntity([
      [Position, { x: 1, y: 2 }],
      [Name, "entity2"],
    ]);

    expect(world.isAlive(entity2)).toBe(true);
    expect(world.hasComponent(entity2, Position)).toBe(false); // NOTE: deferred

    foundData.push({ entity: entity.id(), data });
  });

  expect(entity2).toBeDefined();
  expect(world.isAlive(entity2!)).toBe(true);
  expect(world.hasComponent(entity2!, Position)).toBe(true);

  expect(foundData.length).toBe(1);
});

test("Component addition during query", () => {
  const world = new World();

  const entity = world.addEntity([
    [Position, { x: 1, y: 2 }],
    [Name, "entity1"],
  ]);

  const query = new Query({ pos: Position });
  query.forEach(world, (entity, _data) => {
    world.addComponents(entity.id(), [[Velocity, { x: 1, y: 2 }]]);

    expect(world.hasComponent(entity.id(), Velocity)).toBe(false); // NOTE: deferred until query finishes
  });

  expect(world.hasComponent(entity, Velocity)).toBe(true);
});

test("Changes kept after move", () => {
  const world = new World();

  const entity = world.spawnEntity([[Position, { x: 1, y: 2 }]]);

  entity.addComponent(Name, "entity1");
  const foundData: EntityId[] = [];
  const query = new Query({ pos: Position }).trackModified();

  query.forEach(world, (_entity, _) => {
    foundData.push(_entity.id());
  });

  expect(foundData).toEqual([entity.id()]);
});

test("Granular change tracking", () => {
  const world = new World();

  const entity = world.addEntity([
    [Position, { x: 1, y: 2 }],
    [Velocity, { x: 1, y: 2 }],
    [Name, "entity1"],
  ]);

  world.addEntity([
    [Position, { x: 1, y: 2 }],
    [Velocity, { x: 1, y: 2 }],
    [Name, "entity2"],
  ]);

  const entity3 = world.addEntity([
    [Position, { x: 1, y: 2 }],
    [Velocity, { x: 1, y: 2 }],
    [Name, "entity3"],
  ]);

  const foundData: string[] = [];
  const query = new Query({ pos: Position, name: Name }).trackModified();

  // First run should include all entities
  expect(extractData()).toEqual(["entity1", "entity2", "entity3"]);

  expect(extractData()).toEqual([]);

  world.setComponent(entity, Position, { x: 1, y: 3 });

  expect(extractData()).toEqual(["entity1"]);

  // Now add a new entity
  world.addEntity([
    [Position, { x: 1, y: 2 }],
    [Velocity, { x: 1, y: 2 }],
    [Name, "entity4"],
  ]);

  expect(extractData()).toEqual(["entity4"]);

  // New archetype
  world.addEntity([
    [Position, { x: 1, y: 2 }],
    [Name, "entity5"],
  ]);

  world.setComponent(entity3, Position, { x: 1, y: 3 });

  expect(extractData()).toEqual(["entity3", "entity5"]);

  function extractData(): string[] {
    foundData.length = 0;
    query.forEach(world, (_entity, data) => {
      foundData.push(data.name);
    });

    return foundData;
  }
});

test("Moving entity preserves changes", () => {
  const world = new World();

  world.addEntity([
    [Position, { x: 1, y: 2 }],
    [Velocity, { x: 1, y: 2 }],
    [Name, "entity1"],
  ]);

  const entity2 = world.addEntity([
    [Position, { x: 1, y: 2 }],
    [Velocity, { x: 1, y: 2 }],
    [Name, "entity2"],
  ]);

  world.addEntity([
    [Position, { x: 1, y: 2 }],
    [Color, { x: 1, y: 2, z: 3 }],
    [Velocity, { x: 1, y: 2 }],
    [Name, "entity3"],
  ]);

  const query = new Query({ pos: Position, name: Name }).trackModified();

  expect(extractData()).toEqual(["entity1", "entity2", "entity3"]);

  expect(extractData()).toEqual([]);

  world.addComponents(entity2, [[Position, { x: 1, y: 3 }]]);

  // Force move to new archetype
  world.addComponents(entity2, [[Color, { x: 1, y: 2, z: 3 }]]);

  expect(extractData()).toEqual(["entity2"]);
  expect(extractData()).toEqual([]);

  world.removeComponent(entity2, Position);

  expect(extractData()).toEqual([]);

  function extractData(): string[] {
    const foundData: string[] = [];
    query.forEach(world, (_entity, data) => {
      foundData.push(data.name);
    });

    return foundData;
  }
});

test("Early return in query", () => {
  const world = new World();

  // Create multiple entities
  world.addEntity([
    [Position, { x: 1, y: 1 }],
    [Name, "entity1"],
  ]);

  world.addEntity([
    [Position, { x: 2, y: 2 }],
    [Name, "entity2"],
  ]);

  world.addEntity([
    [Position, { x: 3, y: 3 }],
    [Name, "entity3"],
  ]);

  const query = new Query({ pos: Position, name: Name });
  const visited: string[] = [];

  query.forEach(world, (_entity, data) => {
    visited.push(data.name);

    if (data.name === "entity2") {
      return true;
    }
  });

  // Should have stopped after entity2, so entity3 should not be visited
  expect(visited.length).toBe(2);
  expect(visited).toEqual(["entity1", "entity2"]);
});

test("Early return in change-tracked query", () => {
  const world = new World();

  // Create multiple entities
  world.addEntity([
    [Position, { x: 1, y: 1 }],
    [Name, "entity1"],
  ]);

  world.addEntity([
    [Position, { x: 2, y: 2 }],
    [Name, "entity2"],
  ]);

  world.addEntity([
    [Position, { x: 3, y: 3 }],
    [Name, "entity3"],
  ]);

  // Modifcation tracking uses different iteration mechanism
  const query = new Query({ pos: Position, name: Name }).trackModified();
  const visited: string[] = [];

  query.forEach(world, (_entity, data) => {
    visited.push(data.name);

    if (data.name === "entity2") {
      return true;
    }
  });

  // Should have stopped after entity2, so entity3 should not be visited
  expect(visited.length).toBe(2);
  expect(visited).toEqual(["entity1", "entity2"]);
});

test("Entity removal during query", () => {
  const world = new World();

  // Create 3 entities with health components
  const entity1 = world.addEntity([
    [Position, { x: 1, y: 1 }],
    [Name, "entity1"],
    [Health, 100],
  ]);

  const entity2 = world.addEntity([
    [Position, { x: 2, y: 2 }],
    [Name, "entity2"],
    [Health, 0], // This entity has 0 health and should be removed
  ]);

  const entity3 = world.addEntity([
    [Position, { x: 3, y: 3 }],
    [Name, "entity3"],
    [Health, 50],
  ]);

  const query = new Query({ pos: Position, name: Name, health: Health });

  query.forEach(world, (entity, data) => {
    // Remove entities with 0 health during iteration
    if (data.health === 0) {
      world.removeEntity(entity.id());
    }
  });

  // Assert the 2 remaining entities are alive
  expect(world.isAlive(entity1)).toBe(true);
  expect(world.isAlive(entity3)).toBe(true);

  // Assert the removed entity is dead
  expect(world.isAlive(entity2)).toBe(false);

  const found: number[] = [];
  query.forEach(world, (entity, data) => {
    found.push(data.health);
  });

  expect(found).toEqual([100, 50]);
});
