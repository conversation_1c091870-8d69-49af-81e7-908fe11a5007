import Emittery from "emittery";
import {
  Channel,
  Channels,
  ChannelType,
  CleanupCallback,
  Client,
  ClientEvents,
  ClientOptions,
  ConnectionCloseInfo,
  MessageCallback,
  NetworkStream,
  ProviderType,
} from "../types";
import { StreamMessageReader, StreamMessageWriter } from "../utils";
import { NetworkStats, ChannelStats, ClientStats } from "../core/stats";

const WTQuickTypicalHeader = 12; // Mid-range Quick header size
const WTUDPSize = 28; // UDP header size (IPv4)
const WTMinPacketSize = WTQuickTypicalHeader + WTUDPSize;

/**
 * Calculate the size of the WebTransport datagram that will be sent over the network
 *
 * Based on:
 * https://datatracker.ietf.org/doc/html/rfc6455#section-5.2
 * https://datatracker.ietf.org/doc/html/rfc6455#section-5.3
 *
 * @param byteLength - The length of the byte array to send
 * @returns The size of the packet that will be sent over the WebSocket including the WebSocket headers
 */
const getWTDatagramSize = (byteLength: number): number => {
  return byteLength + WTMinPacketSize;
};

interface ChannelDescriptor {
  reader: StreamMessageReader;
  writer: StreamMessageWriter;
}

class UnreliableUnorderedChannel implements Channel {
  private _messageReader: StreamMessageReader;
  private _messageWriter: StreamMessageWriter;

  private _stats: ChannelStats;

  // private _isDatagramBased: boolean;

  constructor(descriptor: ChannelDescriptor, stats: ChannelStats) {
    this._stats = stats;
    this._messageReader = descriptor.reader;
    this._messageWriter = descriptor.writer;

    // this._isDatagramBased = isDatagramBased;

    this._messageReader.subscribe((data) => {
      // TODO: Calculate the correct size
      this._stats._onReceive(
        getWTDatagramSize(data.byteLength),
        data.byteLength
      );
    });
  }

  public async send(data: Uint8Array): Promise<void> {
    try {
      await this._messageWriter.write(data);

      // TODO: Calculate the correct size
      this._stats._onSend(getWTDatagramSize(data.byteLength), data.byteLength);
    } catch (error) {
      // The session can be closed while we are writing
      // TODO: Handle this more gracefully
      console.error(error);
    }
  }

  public onReceive(cb: MessageCallback): CleanupCallback {
    return this._messageReader.subscribe(cb);
  }

  public get type(): ChannelType {
    return ChannelType.UNRELIABLE_UNORDERED;
  }

  public get stats(): ChannelStats {
    return this._stats;
  }
}

class ReliableOrderedChannel extends UnreliableUnorderedChannel {
  public override get type(): ChannelType {
    return ChannelType.RELIABLE_ORDERED;
  }
}

export class WebTransportClient
  extends Emittery<ClientEvents>
  implements Client
{
  private _wt: WebTransport;
  private _channels = {} as Channels;

  private _stats: ClientStats;

  declare private _reliableOrderedChannel: ReliableOrderedChannel;
  declare private _unreliableUnorderedChannel: UnreliableUnorderedChannel;

  declare private _ready: Promise<void>;
  declare private _closed: Promise<ConnectionCloseInfo>;

  static isSupported(): boolean {
    return typeof WebTransport !== "undefined";
  }

  constructor(options: ClientOptions) {
    super();

    const url = `https://${options.host + (options.port ? `:${options.port}` : "")}${options.path ?? ""}`;

    const wtOptions = options.hashes
      ? {
          serverCertificateHashes: options.hashes,
        }
      : {};
    this._wt = new WebTransport(url, wtOptions);

    this._stats = new ClientStats();

    this.initReadyState();
    this.initCloseState();
    this.initStats();
  }

  private initStats() {
    let rafId: number = -1;
    const onFrame = () => {
      this.updateStats();
      rafId = requestAnimationFrame(onFrame);
    };

    this._ready.then(() => {
      rafId = requestAnimationFrame(onFrame);
    });

    this._closed.then(() => {
      cancelAnimationFrame(rafId);
    });
  }

  private initReadyState() {
    this._ready = this._wt.ready
      .then(() => {
        return Promise.all([
          this.initDatagramChannel(),
          this.initStreamChannel(),
        ]);
      })
      .then(() => {
        this._channels = {
          reliableOrdered: this._reliableOrderedChannel,
          unreliableUnordered: this._unreliableUnorderedChannel,
        };

        this.emit("ready");
      })
      .catch((err) => {
        console.error(err);
      });
  }

  private initCloseState() {
    this._closed = this._wt.closed.then((event) => {
      const closeInfo = {
        closeCode: event.closeCode || 0,
        reason: event.reason || "",
      };

      this.emit("close", closeInfo);

      return closeInfo;
    });
  }

  private async initDatagramChannel(): Promise<void> {
    const stats = new ChannelStats(
      ChannelType.UNRELIABLE_UNORDERED,
      ProviderType.WebTransport,
      this._stats
    );

    const datagramsReader = new StreamMessageReader(
      this._wt.datagrams.readable
    );
    const datagramsWriter = new StreamMessageWriter(
      this._wt.datagrams.writable
    );

    this._unreliableUnorderedChannel = new UnreliableUnorderedChannel(
      {
        reader: datagramsReader,
        writer: datagramsWriter,
      },
      stats
    );

    this._wt.closed.then(() => {
      datagramsReader.dispose();
      datagramsWriter.dispose();
    });
  }

  private async initStreamChannel(): Promise<void> {
    const incomingStreamReader = (
      this._wt.incomingBidirectionalStreams as ReadableStream<NetworkStream>
    ).getReader();

    const { value, done } = await incomingStreamReader.read();

    if (done) {
      throw new Error("Reliable channel cannot be opened");
    }

    const stats = new ChannelStats(
      ChannelType.RELIABLE_ORDERED,
      ProviderType.WebTransport,
      this._stats
    );

    const streamReader = new StreamMessageReader(value.readable);
    const streamWriter = new StreamMessageWriter(value.writable);

    this._reliableOrderedChannel = new ReliableOrderedChannel(
      {
        reader: streamReader,
        writer: streamWriter,
      },
      stats
    );

    this._wt.closed.then(() => {
      streamReader.dispose();
      streamWriter.dispose();
    });
  }

  private _lastTimestamp = 0;
  private updateStats() {
    const now = performance.now();
    const delta = now - this._lastTimestamp;

    if (delta > NetworkStats.STATS_INTERVAL) {
      this._lastTimestamp = now;

      this.channels.reliableOrdered.stats._onCycle(delta);
      this.channels.unreliableUnordered.stats._onCycle(delta);
      this._stats._onCycle(delta);

      this._lastTimestamp = now;
    }
  }

  public get channels(): Readonly<Channels> {
    return this._channels;
  }

  public get MAX_DATAGRAM_SIZE(): number {
    return this._wt.datagrams.maxDatagramSize;
  }

  public get ready(): Promise<void> {
    return this._ready;
  }

  public get closed(): Promise<ConnectionCloseInfo> {
    return this._closed;
  }

  public get provider(): ProviderType {
    return ProviderType.WebTransport;
  }

  public get stats(): ClientStats {
    return this._stats;
  }

  close(info?: ConnectionCloseInfo): void {
    this._wt.close(info);
  }

  public get socket(): WebTransport {
    return this._wt;
  }
}
