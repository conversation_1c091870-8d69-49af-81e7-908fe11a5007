/**
 * Stores and manages singleton services in the World
 */
export class ServiceStore {
  private _services: Map<{ new (...args: any[]): unknown }, unknown> =
    new Map();

  public add<T extends object>(instance: T): void {
    // Register under all parent types
    let prototype = Object.getPrototypeOf(instance);
    while (prototype && prototype !== Object.prototype) {
      const constructor = prototype.constructor;
      if (constructor && constructor !== Object) {
        if (this._services.has(constructor)) {
          throw new Error(`Service ${constructor.name} already registered`);
        }

        this._services.set(constructor, instance);
      }
      prototype = Object.getPrototypeOf(prototype);
    }
  }

  public get<T>(serviceType: new (...args: any[]) => T): T {
    const service = this._services.get(serviceType);
    if (!service) {
      throw new Error(`Service ${serviceType.name} not found`);
    }
    return service as T;
  }

  public has<T>(serviceType: new (...args: any[]) => T): boolean {
    return this._services.has(serviceType);
  }
}
