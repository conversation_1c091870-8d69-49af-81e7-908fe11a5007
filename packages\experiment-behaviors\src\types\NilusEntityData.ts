import type {
  WorldEntityComponentData,
  WorldEntityComponentUUID,
} from "./NilusComponentData";

export type WorldEntityData = {
  readonly uuid: WorldEntityUUID;
  readonly name: string;
  readonly transform: WorldEntityTransformData;
  readonly components: Record<
    WorldEntityComponentUUID,
    WorldEntityComponentData
  >;
};

export type WorldEntityUUID = //
  string & { readonly __worldEntityUUID: unique symbol };

export type WorldEntityTransformData = {
  position: readonly [number, number, number];
  rotation: readonly [number, number, number];
  scale: readonly [number, number, number];
};
