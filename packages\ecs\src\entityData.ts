import { Component } from "./component";
import { EntityId } from "./entity";
import { ComponentTuple, EntityLocation, World } from "./world";

/** Allows fast manipulation and query of a single entity */
export class EntityData {
  private _entity: EntityId;
  private _world: World;
  private _loc: EntityLocation;
  private _tick: number;
  private _deferred: boolean;

  public get entity(): EntityId {
    return this._entity;
  }

  public get loc(): EntityLocation {
    return this._loc;
  }
  constructor(
    entity: EntityId,
    world: World,
    loc: EntityLocation,
    tick: number
  ) {
    this._entity = entity;
    this._world = world;
    this._loc = loc;
    this._tick = tick;
    this._deferred = loc.archetype === world.pendingArchetype;
  }

  refreshIndex(): EntityLocation {
    if (this._world.locTick != this._tick) {
      this._loc = this._world.getLocation(this._entity);
      this._tick = this._world.locTick;
    }

    this._deferred = this._loc.archetype === this._world.pendingArchetype;

    return this._loc;
  }

  public isAlive(): boolean {
    return this._world.isAlive(this._entity);
  }

  /** Returns true if the entity is deferred, meaning it was added during a query iteration and will only be visible after the iteration completes.
   *
   * **NOTE**: Deferred entities do not have any components until added properly to the world
   */
  public isDeferred(): boolean {
    this.refreshIndex();
    return this._deferred;
  }

  public id(): EntityId {
    return this._entity;
  }

  public getComponent<T>(component: Component<T>): T | null {
    const loc = this.refreshIndex();

    return loc.archetype.getComponent(loc.index, component);
  }

  public addComponent<T>(component: Component<T>, value: T) {
    this._world.addComponents(this._entity, [[component, value]]);
  }

  public markDirty(component: Component<unknown>) {
    this._world.markDirty(this._entity, component);
  }

  /** Add a set of components to an entity.
   *
   * **NOTE**: If called during a query iteration, the changes will be deferred and not visible until the end of the iteration.
   */
  public addComponents(componentValues: ComponentTuple<unknown>[]) {
    this._world.addComponents(this._entity, componentValues);
  }

  public removeComponent<T>(component: Component<T>) {
    this._world.removeComponent(this._entity, component);
  }

  /** Sets the component value immediately. Throws if component is not already present on entity */
  public setComponent<T>(component: Component<T>, value: T) {
    this._world.setComponentByLocation(this.refreshIndex(), component, value);
  }

  public hasComponent<T>(component: Component<T>): boolean {
    const loc = this.refreshIndex();
    return loc.archetype.hasComponentForEntity(loc.index, component);
  }

  /** Returns all components on the entity */
  public allComponents(): ComponentTuple<unknown>[] {
    const loc = this.refreshIndex();
    const components: ComponentTuple<unknown>[] = [];

    for (const cell of loc.archetype.cells()) {
      const value = cell.get(loc.index);
      components.push([cell.component, value]);
    }

    for (const sparseCell of loc.archetype.sparseCells()) {
      const value = sparseCell.get(loc.index);
      if (value !== undefined) {
        components.push([sparseCell.component, value]);
      }
    }

    return components;
  }

  display(): string {
    return `EntityData(${this._entity}) {${this.allComponents()
      .map((c) => c[0].NAME)
      .join(", ")}}`;
  }

  toString(): string {
    return this.display();
  }

  /** Collects the entity's component into an entity builder.
   *
   * **NOTE**: does not deep copy the entity's components, use referential types with caution
   */
  toBuilder(): EntityBuilder {
    const builder = new EntityBuilder();
    builder.addComponents(this.allComponents());
    return builder;
  }
}

/**
 * Allows carrying an entity's component outside of the world.
 *
 * Useful for offline construction of entities, to subsequent spawning into the world.
 * */
export class EntityBuilder {
  private components: Map<Component<unknown>, unknown> = new Map();

  constructor() {}

  public addComponent<T>(component: Component<T>, value: T): EntityBuilder {
    this.components.set(component, value);
    return this;
  }

  public addComponents(
    componentValues: ComponentTuple<unknown>[]
  ): EntityBuilder {
    for (const [component, value] of componentValues) {
      this.components.set(component, value);
    }
    return this;
  }

  public getComponent<T>(component: Component<T>): T | null {
    return this.components.get(component) as T | null;
  }

  public getOrAddComponent<T>(
    component: Component<T>,
    defaultValue: () => T
  ): T {
    if (this.components.has(component)) {
      return this.components.get(component) as T;
    }

    const value = defaultValue();
    this.components.set(component, value);
    return value;
  }

  public spawn(world: World): EntityData {
    const values: ComponentTuple<unknown>[] = [];
    for (const [component, value] of this.components) {
      values.push([component, value]);
    }

    return world.spawnEntity(values);
  }

  public addToEntity(entity: EntityId, world: World) {
    const values: ComponentTuple<unknown>[] = [];
    for (const [component, value] of this.components) {
      values.push([component, value]);
    }
    world.addComponents(entity, values);
  }

  public spawnWithId(world: World, id: EntityId): EntityData {
    const values: ComponentTuple<unknown>[] = [];
    for (const [component, value] of this.components) {
      values.push([component, value]);
    }

    return world.spawnEntityWithId(id, values);
  }
}
