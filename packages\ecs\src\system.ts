import { Archetype } from "./archetype";
import { EntityData } from "./entityData";
import { isRequiredByPattern, matchesEntity, QueryPattern } from "./query";
import { EntityLocation, World } from "./world";
import { Component } from "./component";

/** Provided to a system's execution to provide logging and other context */
export class SystemContext {
  private _name: string;
  public deltaTime: number = 0.0;

  constructor(name: string) {
    this._name = name;
  }

  private formatMessage() {
    return `🔩 ${this._name}:`;
  }

  debug(...args: any[]) {
    console.debug(this.formatMessage(), ...args);
  }

  error(...args: any[]) {
    console.error(this.formatMessage(), ...args);
  }

  warn(...args: any[]) {
    console.warn(this.formatMessage(), ...args);
  }

  info(...args: any[]) {
    console.info(this.formatMessage(), ...args);
  }

  fatal(...args: any[]) {
    console.error(this.formatMessage(), ...args);
    throw new Error(this.formatMessage() + " " + args.join(" "));
  }
}

class SystemWithContext {
  system: System;
  context: SystemContext;

  constructor(system: System) {
    this.system = system;
    this.context = new SystemContext(system.name ?? system.constructor.name);
  }
}

/** Systems allow executing logic on the world and modifying data, as well as reacting to changes.
 *
 * A system can run some logic each frame, and execute queries on the world to select and modify entity data.
 *
 * They can also listen to component and entity creation and removal based on their specific changes.
 */
export interface System {
  entityFilter?: QueryPattern;
  name?: string;

  run?(world: World, systemContext: SystemContext): void;

  init?(world: World, systemContext: SystemContext): void;

  onEntityAdded?(
    world: World,
    entity: EntityData,
    systemContext: SystemContext
  ): void;

  onEntityRemoved?(
    world: World,
    entity: EntityData,
    systemContext: SystemContext
  ): void;
}

export class SystemRunner {
  runSystems: SystemWithContext[];
  entityPatterns: { pattern: QueryPattern; system: SystemWithContext }[];
  initialized: boolean = false;

  constructor(systems: System[] = []) {
    const systemsWithContext = systems.map(
      (system) => new SystemWithContext(system)
    );

    this.runSystems = systemsWithContext.filter(
      (system) => system.system.run !== undefined
    );

    this.entityPatterns = [];
    for (const system of systemsWithContext) {
      if (system.system.entityFilter) {
        this.entityPatterns.push({
          pattern: system.system.entityFilter,
          system,
        });
      }
    }
  }

  onAddEntity(world: World, entity: EntityData) {
    world.ensureUnlocked();

    for (const { pattern, system } of this.entityPatterns) {
      if (matchesEntity(pattern, entity.loc)) {
        system.system.onEntityAdded?.(world, entity, system.context);
      }
    }
  }

  onEntityMoved(
    world: World,
    entity: EntityData,
    oldArchetype: Archetype,
    newLoc: EntityLocation,
    change: Component<unknown>[],
    removed: boolean
  ) {
    for (const { pattern, system } of this.entityPatterns) {
      // If, by definition, we added a component that is strictly *required* by the pattern, that means it must not have matched before
      const changedRequired = change.some((x) =>
        isRequiredByPattern(pattern, x)
      );

      if (!removed && changedRequired) {
        const matchesCurrent = matchesEntity(pattern, newLoc);
        if (matchesCurrent) {
          system.system.onEntityAdded?.(world, entity, system.context);
        }
      } else if (removed && changedRequired) {
        const matchedBefore = matchesEntity(pattern, newLoc, change);

        if (matchedBefore) {
          system.system.onEntityRemoved?.(world, entity, system.context);
        }
      }
    }
  }

  onRemoveEntity(world: World, entity: EntityData) {
    for (const { pattern, system } of this.entityPatterns) {
      if (matchesEntity(pattern, entity.loc)) {
        system.system.onEntityRemoved?.(world, entity, system.context);
      }
    }
  }

  init(world: World) {
    for (const system of this.runSystems) {
      system.system.init?.(world, system.context);
    }
  }

  /** Runs all the systems update methods */
  run(world: World, deltaTime: number) {
    world.ensureUnlocked();

    if (!this.initialized) {
      this.init(world);
      this.initialized = true;
    }

    for (const system of this.runSystems) {
      try {
        system.context.deltaTime = deltaTime;
        system.system.run!(world, system.context);
      } catch (e) {
        system.context.error(`Exception when running system: ${e}`);
        throw e;
      }
    }
  }
}
