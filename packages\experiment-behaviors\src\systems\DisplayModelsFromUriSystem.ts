import { Object3D } from "three";

import {
  LoadingIndicator,
  loadingSpinnerFactoryService,
} from "@nilo/experiment-behaviors/services/LoadingSpinnerFactoryService";
import { modelLoadingService } from "@nilo/experiment-behaviors/services/ModelLoadingService";
import { ThreejsContainersSystem } from "@nilo/experiment-behaviors/systems/ThreejsContainersSystem";
import { BuiltInComponentTypes } from "@nilo/experiment-behaviors/types/BuiltInComponentData";
import { createMulticaster } from "@nilo/experiment-behaviors/utils/createMulticaster";

import type { EcsSystemContext } from "@nilo/experiment-behaviors/system-context/createSystemContext";
import type {
  BuiltInComponentData,
  ModelURI,
} from "@nilo/experiment-behaviors/types/BuiltInComponentData";
import type {
  EcsSystem,
  EcsSystemApi,
} from "@nilo/experiment-behaviors/types/EcsSystem";
import type { WorldEntityComponentUUID } from "@nilo/experiment-behaviors/types/NilusComponentData";
import type { WorldEntityUUID } from "@nilo/experiment-behaviors/types/NilusEntityData";

type ModelLoadedEvent = {
  readonly entityUuid: WorldEntityUUID;
  readonly componentUuid: WorldEntityComponentUUID;
  readonly modelObject: Object3D;
  readonly uri: ModelURI;
};

const CHILD_KEY_PREFIX = "model-from-uri";
const LOADING_INDICATOR_CHILD_KEY = "loading-indicator";

export class DisplayModelsFromUriSystem implements EcsSystem {
  private readonly modelControllers = new Map<
    WorldEntityUUID,
    ModelController
  >();
  private readonly onDispose = createMulticaster();

  public readonly events = {
    onModelLoaded: createMulticaster<[event: ModelLoadedEvent]>(),
  };

  initialize({ events }: EcsSystemContext) {
    events.onEntityComponentAddedByType.add(
      BuiltInComponentTypes.modelFromUri,
      async (context, entityData, componentData) => {
        const containersApi = //
          context.getRequiredSystemApiByConstructor(ThreejsContainersSystem);

        const existingCtrl = this.modelControllers.get(entityData.uuid);
        if (existingCtrl) {
          console.warn(
            "🔶 Entity already has a model, skipping:",
            entityData.uuid
          );
          return;
        }

        const ctrl = new ModelController(
          entityData.uuid,
          componentData,
          containersApi,
          (event) => this.events.onModelLoaded.invoke(event),
          (error) => context.handleError(error, "Failed to load model")
        );

        this.modelControllers.set(entityData.uuid, ctrl);
        await ctrl.loadModel();
      }
    );

    events.onEntityComponentRemovedByType.add(
      BuiltInComponentTypes.modelFromUri,
      async (_context, entityData) => {
        const ctrl = this.modelControllers.get(entityData.uuid);
        if (ctrl) {
          await ctrl.cleanup();
          this.modelControllers.delete(entityData.uuid);
          console.debug(`➖ Model removed from entity ${entityData.uuid}`);
        }
      }
    );

    events.onEntityComponentUpdatedByType.add(
      BuiltInComponentTypes.modelFromUri,
      (_context, entityData, componentData) => {
        const ctrl = this.modelControllers.get(entityData.uuid);
        if (!ctrl) {
          console.warn(
            "🔶 Entity has no model controller, skipping:",
            entityData.uuid
          );
          return;
        }

        ctrl.onComponentParamsChange(componentData.params);
      }
    );

    events.onFrame.add((_context, deltaTime) => {
      for (const ctrl of this.modelControllers.values()) {
        ctrl.updateLoadingIndicator(deltaTime);
      }
    });
  }

  public getApi(_context: EcsSystemContext) {
    return {
      getLoadedModel: (uuid: WorldEntityUUID) => {
        const ctrl = this.modelControllers.get(uuid);
        return ctrl?.getModelObject();
      },
      events: this.events,
    };
  }

  dispose() {
    for (const ctrl of this.modelControllers.values()) {
      ctrl.cleanup();
    }

    this.modelControllers.clear();
    this.events.onModelLoaded.clear();
    this.onDispose.invoke();
  }
}

class ModelController {
  private loadingIndicator?: LoadingIndicator;
  private modelObject?: Object3D;
  private lastLoadedUri?: ModelURI;

  private readonly childKey: string;

  constructor(
    private readonly entityUuid: WorldEntityUUID,
    private readonly componentData: BuiltInComponentData<
      typeof BuiltInComponentTypes.modelFromUri
    >,
    private readonly containersApi: EcsSystemApi<ThreejsContainersSystem>,
    private readonly onModelLoaded: (event: ModelLoadedEvent) => void,
    private readonly onError: (error: unknown) => void
  ) {
    this.childKey = `${CHILD_KEY_PREFIX}:${componentData.uuid}`;
  }

  getModelObject() {
    return this.modelObject;
  }

  async loadModel() {
    const uri = this.componentData.params.uri;
    if (uri === this.lastLoadedUri) return;

    await this.cleanup();
    await this.showLoadingIndicator();

    try {
      const modelObject = await modelLoadingService.loadModel(uri);
      modelObject.userData.name = `model: ${uri.split("/").pop() ?? "undefined"}`;

      this.modelObject = modelObject;
      this.lastLoadedUri = uri;

      this.containersApi.addThreejsChildToEntity(
        this.entityUuid,
        modelObject,
        this.childKey
      );

      this.onModelLoaded({
        entityUuid: this.entityUuid,
        componentUuid: this.componentData.uuid,
        modelObject,
        uri,
      });

      console.debug(`✨ Model loaded: ${uri}`);
    } catch (error) {
      this.onError(error);
      await this.cleanup();
    } finally {
      await this.hideLoadingIndicator();
    }
  }

  private async showLoadingIndicator() {
    const indicator = loadingSpinnerFactoryService.createLoadingIndicator();
    this.loadingIndicator = indicator;
    this.containersApi.addThreejsChildToEntity(
      this.entityUuid,
      indicator.mesh,
      LOADING_INDICATOR_CHILD_KEY
    );
  }

  private async hideLoadingIndicator() {
    if (this.loadingIndicator) {
      this.containersApi.removeThreejsChildFromEntity(
        this.entityUuid,
        LOADING_INDICATOR_CHILD_KEY
      );
      this.loadingIndicator = undefined;
    }
  }

  updateLoadingIndicator(deltaTime: number) {
    this.loadingIndicator?.advanceTime(deltaTime);
  }

  async cleanup() {
    await this.hideLoadingIndicator();

    if (this.modelObject) {
      this.containersApi.removeThreejsChildFromEntity(
        this.entityUuid,
        this.childKey
      );
      this.modelObject = undefined;
    }

    this.lastLoadedUri = undefined;
  }

  onComponentParamsChange(
    updates: Partial<
      BuiltInComponentData<typeof BuiltInComponentTypes.modelFromUri>["params"]
    >
  ) {
    if (updates.uri && updates.uri !== this.lastLoadedUri) {
      console.debug(
        `🔄 URI changed for entity ${this.entityUuid}: from '${this.lastLoadedUri}' to '${updates.uri}'. Reloading.`
      );
      this.loadModel();
    }
  }
}
