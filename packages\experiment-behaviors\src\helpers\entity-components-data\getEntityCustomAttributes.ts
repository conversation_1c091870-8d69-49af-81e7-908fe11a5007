import { findAllEntityComponentsDataOfType } from "./findAllEntityComponentsDataOfType";
import { BuiltInComponentTypes } from "@nilo/experiment-behaviors/types/BuiltInComponentData";
import type { WorldEntityData } from "@nilo/experiment-behaviors/types/NilusEntityData";

export function getEntityCustomAttributes<
  T extends Record<string, unknown> = Record<string, unknown>,
>(entityData: WorldEntityData | null | undefined): T {
  const customAttrComponents = findAllEntityComponentsDataOfType(
    entityData,
    BuiltInComponentTypes.customAttributes
  );

  return customAttrComponents.reduce<T>(
    (acc, component) => ({
      ...acc,
      ...component.params,
    }),
    {} as T
  );
}
