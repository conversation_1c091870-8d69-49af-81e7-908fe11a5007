import { EntityBuilder, EntityData } from "./entityData";
import { Name } from "./component";

/** A behavior represents a set of related components describing an end functionality
 *
 * Behaviors are considered immutable.
 */
export interface Behavior {
  allowMultiple?: boolean;

  /// Add necessary components to an entity
  addToEntity(entity: EntityBuilder): void;

  dependencies?(): BehaviorConstructor[];

  /// Extract this behavior from an entity and return a new separate instance of the behavior
  getFromEntity(entity: EntityData): this;
}

export class NameBehavior implements Behavior {
  public readonly name: string;

  constructor(name?: string) {
    this.name = name ?? "unnamed";
  }

  getFromEntity(entity: EntityData): this {
    const name = entity.getComponent(Name) ?? this.name;
    return new NameBehavior(name) as this;
  }

  addToEntity(entity: EntityBuilder): void {
    entity.addComponents([[Name, this.name]]);
  }
}

export type BehaviorConstructor = new () => Behavior;
