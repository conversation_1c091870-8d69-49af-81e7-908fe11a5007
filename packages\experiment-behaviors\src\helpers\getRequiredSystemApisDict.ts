import type { EcsSystem } from "../types/EcsSystem";
import type { EcsSystemContext } from "../system-context/createSystemContext";

type SystemConstructor<T extends EcsSystem> = new (...args: any[]) => T;

export function getRequiredSystemApisDict<
  T extends { [K: string]: SystemConstructor<EcsSystem> },
>(
  context: EcsSystemContext,
  systemConstructors: T
): { [K in keyof T]: InstanceType<T[K]> } {
  return Object.entries(systemConstructors).reduce(
    (acc, [name, constructor]) => {
      const api = context.getRequiredSystemApiByConstructor(constructor);
      // @ts-ignore - we know this is safe due to the type constraints
      acc[name] = api;
      return acc;
    },
    {} as { [K in keyof T]: InstanceType<T[K]> }
  );
}
