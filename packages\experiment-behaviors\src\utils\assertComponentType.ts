import {
  BuiltInComponentData,
  BuiltInComponentType,
} from "../types/BuiltInComponentData";
import { WorldEntityComponentData } from "../types/NilusComponentData";

export function assertComponentType<Type extends string | BuiltInComponentType>(
  componentData: Type extends BuiltInComponentType
    ? BuiltInComponentData<Type>
    : WorldEntityComponentData,
  type: Type
): asserts componentData is Type extends BuiltInComponentType
  ? BuiltInComponentData<Type>
  : WorldEntityComponentData {
  if (componentData.type !== type) {
    throw new Error(
      `Component type mismatch: expected ${type}, got ${componentData.type}`
    );
  }
}

export function isComponentType<Type extends string | BuiltInComponentType>(
  componentData: Type extends BuiltInComponentType
    ? BuiltInComponentData<Type>
    : WorldEntityComponentData,
  type: Type
): componentData is Type extends BuiltInComponentType
  ? BuiltInComponentData<Type>
  : WorldEntityComponentData {
  return componentData.type === type;
}
