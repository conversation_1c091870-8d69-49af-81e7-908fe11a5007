import { Mesh, MeshStandardMaterial, TorusGeometry } from "three";

function createLoadingSpinnerFactoryService() {
  function createLoadingIndicator() {
    const geometry = new TorusGeometry(0.25, 0.01, 16, 32);
    const material = new MeshStandardMaterial({ color: 0x0066ff });
    const mesh = new Mesh(geometry, material);

    return {
      mesh,
      advanceTime(deltaTime: number) {
        mesh.rotation.y += deltaTime * 9;
        mesh.rotation.x += deltaTime * 5;
        mesh.rotation.z += deltaTime * 2;
      },
    };
  }

  return {
    createLoadingIndicator,
  };
}

export type LoadingSpinnerFactoryService = ReturnType<
  typeof createLoadingSpinnerFactoryService
>;

export type LoadingIndicator = ReturnType<
  LoadingSpinnerFactoryService["createLoadingIndicator"]
>;

// Export singleton instance
export const loadingSpinnerFactoryService =
  createLoadingSpinnerFactoryService();
