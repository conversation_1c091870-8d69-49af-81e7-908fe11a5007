import {
  DoubleSide,
  Mesh,
  ShaderMaterial,
  ShaderMaterialParameters,
  SphereGeometry,
  Texture,
  Group,
  AdditiveBlending,
  Color,
} from "three";

import { fragmentShader } from "./shaders/fragment.glsl";
import { vertexShader } from "./shaders/vertex.glsl";

export interface PortalOrbOpts {
  radius?: number;
  height?: number;
  segments?: number;
  envMap: Texture;
  offsetX?: number;
  rippleNoiseScale?: number;
  timeScale?: number;
  textureDistortionAmount?: number;
  textureDistortionTimeScale?: number;
  glowColor?: string;
  glowIntensity?: number;
}

export type PortalOrb = ReturnType<typeof createPortalOrb>;

/**
 * Creates a portal orb mesh with bubbling, wavy displacement.
 * Must animate by incrementing mesh.material.uniforms.time.value each frame.
 */
export function createPortalOrb({
  radius = 1,
  height,
  segments = 128,
  envMap,
  offsetX = 0,
  rippleNoiseScale = 4.5,
  timeScale = 1,
  textureDistortionAmount = 0,
  textureDistortionTimeScale = 1,
  glowColor = "#7f7fff",
  glowIntensity = 0.5,
}: PortalOrbOpts) {
  // Create a group to hold both meshes
  const group = new Group();

  // 1) geometry - use height to create ellipsoid
  const geometry = new SphereGeometry(radius, segments, segments);
  // If height is not provided, use default sphere height (diameter)
  const targetHeight = height !== undefined ? height : radius * 2;
  geometry.scale(1, targetHeight / (radius * 2), 1);

  // 2) shared uniforms
  const uniforms = {
    time: { value: 0 },
    envMap: { value: envMap },
    offsetX: { value: offsetX },
    rippleNoiseScale: { value: rippleNoiseScale },
    textureDistortionAmount: { value: textureDistortionAmount },
    textureDistortionTimeScale: { value: textureDistortionTimeScale },
  };

  // 3) shader material
  const material = new ShaderMaterial({
    vertexShader,
    fragmentShader,
    uniforms,
    side: DoubleSide,
  } as ShaderMaterialParameters);

  const mesh = new Mesh(geometry, material);
  group.add(mesh);

  // Create glow mesh
  const glowGeometry = new SphereGeometry(radius * 1.05, segments, segments);
  glowGeometry.scale(1, targetHeight / (radius * 2), 1);

  const glowMaterial = new ShaderMaterial({
    vertexShader,
    fragmentShader,
    uniforms: {
      ...uniforms,
      glowColor: { value: new Color(glowColor) },
      glowIntensity: { value: glowIntensity },
    },
    side: DoubleSide,
    transparent: true,
    blending: AdditiveBlending,
    depthWrite: false,
  } as ShaderMaterialParameters);

  const glowMesh = new Mesh(glowGeometry, glowMaterial);
  group.add(glowMesh);

  let rafHandle: number | null = null;
  const onEnterFrame = () => {
    const nowSecondsScaled = performance.now() * 0.001;
    material.uniforms.time.value = nowSecondsScaled * timeScale;
    material.uniforms.textureDistortionTimeScale.value =
      nowSecondsScaled * textureDistortionTimeScale;
    glowMaterial.uniforms.time.value = nowSecondsScaled * timeScale;
    glowMaterial.uniforms.textureDistortionTimeScale.value =
      nowSecondsScaled * textureDistortionTimeScale;
    rafHandle = requestAnimationFrame(onEnterFrame);
  };
  const startTimeLoop = () => {
    onEnterFrame();
  };
  const stopTimeLoop = () => {
    if (rafHandle) {
      cancelAnimationFrame(rafHandle);
      rafHandle = null;
    }
  };

  const updateProps = (props: Partial<PortalOrbOpts>) => {
    if (props.timeScale !== undefined) {
      timeScale = props.timeScale;
    }
    if (props.rippleNoiseScale !== undefined) {
      material.uniforms.rippleNoiseScale.value = props.rippleNoiseScale;
      glowMaterial.uniforms.rippleNoiseScale.value = props.rippleNoiseScale;
    }
    if (props.envMap !== undefined) {
      material.uniforms.envMap.value = props.envMap;
      glowMaterial.uniforms.envMap.value = props.envMap;
    }
    if (props.offsetX !== undefined) {
      material.uniforms.offsetX.value = props.offsetX;
      glowMaterial.uniforms.offsetX.value = props.offsetX;
    }
    if (props.textureDistortionAmount !== undefined) {
      material.uniforms.textureDistortionAmount.value =
        props.textureDistortionAmount;
      glowMaterial.uniforms.textureDistortionAmount.value =
        props.textureDistortionAmount;
    }
    if (props.textureDistortionTimeScale !== undefined) {
      material.uniforms.textureDistortionTimeScale.value =
        props.textureDistortionTimeScale;
      glowMaterial.uniforms.textureDistortionTimeScale.value =
        props.textureDistortionTimeScale;
    }
    if (props.glowColor !== undefined) {
      glowMaterial.uniforms.glowColor.value.set(props.glowColor);
    }
    if (props.glowIntensity !== undefined) {
      glowMaterial.uniforms.glowIntensity.value = props.glowIntensity;
    }
  };

  return {
    mesh: group,
    startTimeLoop,
    stopTimeLoop,
    updateProps,
  };
}
