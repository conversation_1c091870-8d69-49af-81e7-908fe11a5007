import { createMulticaster } from "@nilo/experiment-behaviors/utils/createMulticaster";
import { BuiltInComponentTypes } from "@nilo/experiment-behaviors/types/BuiltInComponentData";

import type { EcsSystemContext } from "@nilo/experiment-behaviors/system-context/createSystemContext";
import type { BuiltInComponentData } from "@nilo/experiment-behaviors/types/BuiltInComponentData";
import type { EcsSystem } from "@nilo/experiment-behaviors/types/EcsSystem";
import type { WorldEntityUUID } from "@nilo/experiment-behaviors/types/NilusEntityData";

export type CustomAttributeChangeEvent = {
  entityUuid: WorldEntityUUID;
  componentUuid: string;
  key: string;
  oldValue: unknown;
  newValue: unknown;
};

export class CustomAttributesSystem implements EcsSystem {
  private readonly onDispose = createMulticaster();
  private readonly attributeControllers = new Map<
    WorldEntityUUID,
    AttributeController
  >();

  public readonly events = {
    onAttributeChanged:
      createMulticaster<[event: CustomAttributeChangeEvent]>(),
  };

  initialize({ events }: EcsSystemContext) {
    events.onEntityComponentAddedByType.add(
      BuiltInComponentTypes.customAttributes,
      (_context, entityData, componentData) => {
        const controller = new AttributeController(
          entityData.uuid,
          componentData,
          (event) => this.events.onAttributeChanged.invoke(event)
        );
        this.attributeControllers.set(entityData.uuid, controller);
      }
    );

    events.onEntityComponentRemovedByType.add(
      BuiltInComponentTypes.customAttributes,
      (_context, entityData) => {
        const controller = this.attributeControllers.get(entityData.uuid);
        if (controller) {
          controller.dispose();
          this.attributeControllers.delete(entityData.uuid);
        }
      }
    );

    events.onEntityComponentUpdatedByType.add(
      BuiltInComponentTypes.customAttributes,
      (_context, entityData, componentData) => {
        const controller = this.attributeControllers.get(entityData.uuid);
        if (!controller) {
          console.warn(
            "🔶 Entity has no attributes controller, skipping:",
            entityData.uuid
          );
          return;
        }
        controller.onComponentParamsChange(componentData.params);
      }
    );
  }

  dispose() {
    for (const controller of this.attributeControllers.values()) {
      controller.dispose();
    }
    this.attributeControllers.clear();
    this.events.onAttributeChanged.clear();
    this.onDispose.invoke();
  }

  public getApi(_context: EcsSystemContext) {
    return {
      events: this.events,
      getAttributes: (entityUuid: WorldEntityUUID) => {
        const controller = this.attributeControllers.get(entityUuid);
        return controller?.getAttributes() ?? {};
      },
      setAttribute: (
        entityUuid: WorldEntityUUID,
        key: string,
        value: unknown
      ) => {
        const controller = this.attributeControllers.get(entityUuid);
        if (!controller) {
          console.warn("🔶 Entity has no attributes controller:", entityUuid);
          return false;
        }
        return controller.setAttribute(key, value);
      },
    };
  }
}

class AttributeController {
  private attributes: Record<string, unknown>;

  constructor(
    private readonly entityUuid: WorldEntityUUID,
    private readonly componentData: BuiltInComponentData<
      typeof BuiltInComponentTypes.customAttributes
    >,
    private readonly onAttributeChanged: (
      event: CustomAttributeChangeEvent
    ) => void
  ) {
    this.attributes = { ...componentData.params };
  }

  getAttributes(): Record<string, unknown> {
    return { ...this.attributes };
  }

  setAttribute(key: string, value: unknown): boolean {
    const oldValue = this.attributes[key];
    if (oldValue === value) return false;

    this.attributes[key] = value;
    this.onAttributeChanged({
      entityUuid: this.entityUuid,
      componentUuid: this.componentData.uuid,
      key,
      oldValue,
      newValue: value,
    });
    return true;
  }

  onComponentParamsChange(
    updates: Partial<
      BuiltInComponentData<
        typeof BuiltInComponentTypes.customAttributes
      >["params"]
    >
  ) {
    if (!updates) return;

    // Compare and emit events for each changed attribute
    for (const [key, newValue] of Object.entries(updates)) {
      const oldValue = this.attributes[key];
      if (oldValue !== newValue) {
        this.attributes[key] = newValue;
        this.onAttributeChanged({
          entityUuid: this.entityUuid,
          componentUuid: this.componentData.uuid,
          key,
          oldValue,
          newValue,
        });
      }
    }

    // Remove attributes that were deleted
    for (const key of Object.keys(this.attributes)) {
      if (!(key in updates)) {
        const oldValue = this.attributes[key];
        delete this.attributes[key];
        this.onAttributeChanged({
          entityUuid: this.entityUuid,
          componentUuid: this.componentData.uuid,
          key,
          oldValue,
          newValue: undefined,
        });
      }
    }
  }

  dispose() {
    // Clear all attributes and emit removal events
    for (const [key, value] of Object.entries(this.attributes)) {
      this.onAttributeChanged({
        entityUuid: this.entityUuid,
        componentUuid: this.componentData.uuid,
        key,
        oldValue: value,
        newValue: undefined,
      });
    }
    this.attributes = {};
  }
}
