name: Deploy Firebase Functions
on:
  workflow_dispatch: {}
  push:
    branches:
      - main
    paths:
      - .github/workflows/firebase-deploy-functions.yml
      - serverless/functions/**
  pull_request:
    types:
      - opened
      - reopened
      - synchronize

concurrency:
  group: firebase-functions-${{ github.head_ref || github.ref_name }}

jobs:
  # this creates a database for pull request's isolated environment
  create_database_and_deploy_rules:
    # only run this step for pull requests
    if: github.event_name == 'pull_request'
    runs-on: ubuntu-latest
    permissions:
      contents: read
      id-token: write
    steps:
      - uses: actions/checkout@v4
      - uses: google-github-actions/auth@v2
        with:
          # Note: these are not secrets
          service_account: "<EMAIL>"
          workload_identity_provider: "projects/************/locations/global/workloadIdentityPools/github/providers/nilo-technologies-org"
      - uses: pnpm/action-setup@v4
        with:
          version: 10.14.0
      - uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: pnpm
      - run: pnpm install
      - name: Get isolated environment component names
        id: isolated-env
        run: |
          pnpm exec isolated-environments emitGithubActionVars "${{ github.head_ref || github.ref_name }}" | tee $GITHUB_OUTPUT
      - name: Create database
        run: |
          if pnpm exec firebase firestore:databases:get ${{ steps.isolated-env.outputs.database }} &> /dev/null; then
            echo "Database already exists"
          else
            echo "Creating database"
            pnpm exec firebase firestore:databases:create --location eur3 ${{ steps.isolated-env.outputs.database }}
          fi
      - name: Update firebase.json
        run: |
          jq \
            --arg database "${{ steps.isolated-env.outputs.database }}" \
            --arg target "${{ steps.isolated-env.outputs.name }}" \
            --arg bucket "${{ steps.isolated-env.outputs.bucket }}" \
            '.firestore[0].database = $database | .storage[0].target = $target | .storage[0].bucket = $bucket' \
            firebase.json > firebase.json.new
          mv firebase.json.new firebase.json
          cat firebase.json
      - name: Deploy rules and indexes
        run: |
          pnpm exec firebase deploy --non-interactive --force --only firestore:${{ steps.isolated-env.outputs.database }} || (sleep 30 && pnpm exec firebase deploy --non-interactive --force --only firestore:${{ steps.isolated-env.outputs.database }})

  # this deploys the functions (both isolated and main)
  deploy_functions:
    runs-on: ubuntu-latest
    permissions:
      contents: "read"
      id-token: "write"
    steps:
      - uses: actions/checkout@v4
      - uses: google-github-actions/auth@v2
        with:
          # Note: these are not secrets
          service_account: "<EMAIL>"
          workload_identity_provider: "projects/************/locations/global/workloadIdentityPools/github/providers/nilo-technologies-org"
      - uses: pnpm/action-setup@v4
        with:
          version: 10.14.0
      - uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: pnpm
      - run: pnpm install
      - name: Get isolated environment component names
        id: isolated-env
        run: |
          pnpm exec isolated-environments emitGithubActionVars "${{ github.head_ref || github.ref_name }}" | tee $GITHUB_OUTPUT
      - name: Emit isolated environment settings into functions code
        run: |
          pnpm exec isolated-environments emitEnvCode "${{ steps.isolated-env.outputs.name }}" > serverless/functions/src/isolatedEnv.ts
      - run: |
          pnpm exec firebase deploy --non-interactive --force --debug --only functions:${{ steps.isolated-env.outputs.functions }} || (sleep 30 && pnpm exec firebase deploy --non-interactive --force --debug --only functions:${{ steps.isolated-env.outputs.functions }})
