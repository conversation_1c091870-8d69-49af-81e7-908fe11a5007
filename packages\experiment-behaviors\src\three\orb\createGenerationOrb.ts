import {
  DoubleSide,
  Mesh,
  ShaderMaterial,
  ShaderMaterialParameters,
  SphereGeometry,
  Texture,
} from "three";
import { vertexShader } from "./shaders/vertex.glsl";
import { fragmentShader } from "./shaders/fragment.glsl";

export interface LiquidMetalSphereOpts {
  radius?: number;
  segments?: number;
  envMapA: Texture;
  envMapB: Texture;
  textureBlendFactor?: number;
  bubbleNoiseScale?: number;
  rippleNoiseScale?: number;
  timeScale?: number;
  offsetA?: number;
  offsetB?: number;
  textureDistortionAmount?: number;
  textureDistortionOffset?: number;
  textureDistortionTimeScale?: number;
}

export type LiquidMetalSphere = ReturnType<typeof createGenerationOrb>;

/**
 * Creates a "liquid metal" sphere mesh with bubbling, wavy displacement.
 * Must animate by incrementing mesh.material.uniforms.time.value each frame.
 */
export function createGenerationOrb({
  radius = 1,
  segments = 128,
  envMapA,
  envMapB,
  textureBlendFactor = 0,
  bubbleNoiseScale = 1.2,
  rippleNoiseScale = 4.5,
  timeScale = 1,
  offsetA = 0,
  offsetB = 0,
  textureDistortionAmount = 0,
  textureDistortionOffset = 0,
  textureDistortionTimeScale = 1,
}: LiquidMetalSphereOpts) {
  // 1) geometry
  const geometry = new SphereGeometry(radius, segments, segments);

  // 2) shared uniforms
  const uniforms = {
    time: { value: 0 },
    envMapA: { value: envMapA },
    envMapB: { value: envMapB },
    textureBlendFactor: { value: textureBlendFactor },
    bubbleNoiseScale: { value: bubbleNoiseScale },
    rippleNoiseScale: { value: rippleNoiseScale },
    offsetA: { value: offsetA },
    offsetB: { value: offsetB },
    textureDistortionAmount: { value: textureDistortionAmount },
    textureDistortionOffset: { value: textureDistortionOffset },
  };

  // 3) shader material
  const material = new ShaderMaterial({
    vertexShader,
    fragmentShader,
    uniforms,
    side: DoubleSide,
  } as ShaderMaterialParameters);

  const mesh = new Mesh(geometry, material);

  let rafHandle: number | null = null;
  const onEnterFrame = () => {
    const nowSecondsScaled = performance.now() * 0.001;
    material.uniforms.time.value = nowSecondsScaled * timeScale;
    material.uniforms.textureDistortionOffset.value =
      nowSecondsScaled * textureDistortionTimeScale;
    rafHandle = requestAnimationFrame(onEnterFrame);
  };
  const startTimeLoop = () => {
    onEnterFrame();
  };
  const stopTimeLoop = () => {
    if (rafHandle) {
      cancelAnimationFrame(rafHandle);
      rafHandle = null;
    }
  };

  const updateProps = (props: Partial<LiquidMetalSphereOpts>) => {
    if (props.timeScale !== undefined) {
      timeScale = props.timeScale;
    }

    if (props.textureBlendFactor !== undefined) {
      material.uniforms.textureBlendFactor.value = props.textureBlendFactor;
    }
    if (props.bubbleNoiseScale !== undefined) {
      material.uniforms.bubbleNoiseScale.value = props.bubbleNoiseScale;
    }
    if (props.rippleNoiseScale !== undefined) {
      material.uniforms.rippleNoiseScale.value = props.rippleNoiseScale;
    }
    if (props.envMapA !== undefined) {
      material.uniforms.envMapA.value = props.envMapA;
    }
    if (props.envMapB !== undefined) {
      material.uniforms.envMapB.value = props.envMapB;
    }
    if (props.offsetA !== undefined) {
      material.uniforms.offsetA.value = props.offsetA;
    }
    if (props.offsetB !== undefined) {
      material.uniforms.offsetB.value = props.offsetB;
    }
    if (props.textureDistortionAmount !== undefined) {
      material.uniforms.textureDistortionAmount.value =
        props.textureDistortionAmount;
    }
    if (props.textureDistortionOffset !== undefined) {
      material.uniforms.textureDistortionOffset.value =
        props.textureDistortionOffset;
    }
  };

  return {
    mesh,
    startTimeLoop,
    stopTimeLoop,
    updateProps,
  };
}
