import type { WorldEntityComponentUUID } from "./NilusComponentData";

interface BuiltInComponentParams {
  worldGroundAsInfinitePlane: {
    textureUri: string;
  };
  modelFromUri: {
    uri: ModelURI;
  };
  generationBlob: {
    prompt: string | null;
    previewImageUri: string | null;
    isGenerating: boolean;
  };
  worldPortal: {
    textureUri: string;
    roomId: string;
  };
  sprite: {
    textureUri: string;
    blendMode: "normal" | "additive";
    size: readonly [number, number];
    opacity?: number; // 0.0 to 1.0, defaults to 1.0
    rotation?: number; // rotation in radians, defaults to 0
  };
  materialOverride: {
    preset: "metal" | "glass"; // e.g., "metal"
    color?: string; // e.g., "ff00ff" or "#ff00ff"
  };
  customBehavior: {
    onFrameCode?: string; // JS code to execute on frame
    // Future: onClickCode?: string;
    // Future: onCollisionCode?: string;
  };
  animationLoop: {
    clip: string; // Name of the animation clip to play
  };
  svgPlane: {
    htmlCode: string; // SVG/HTML code to render
    offsetY: number; // Vertical offset from entity position
    billboard?: boolean; // If true, renders as a sprite that always faces camera
  };
  pointParticleEmitter: {
    emitterShape?: string; // Optional. Format: "point:x,y,z" | "box:x1,y1,z1,x2,y2,z2" | "ellipsoid:cx,cy,cz,rx,ry,rz". Defaults to "point:0,0,0" ;; TODO: implement
    emissionRate: number; // particles per second
    particleLifetime: number; // seconds
    maxParticles: number;

    textureUri: string;
    blending: "normal" | "additive"; // Additive is common for particles
    color: string; // hex color, e.g., "#FFFFFF"

    gravity: readonly [number, number, number]; // e.g., [0, -9.8, 0]
    velocityStart: readonly [number, number, number]; // e.g., [0, 1, 0] for upward
    velocityEnd: readonly [number, number, number]; // e.g., [0, 0, 0] for slowing down
    velocityRandomness: readonly [number, number, number]; // +/- range for each velocity component
    size: number;
    alphaStart: number; // 0.0 to 1.0
    alphaEnd: number; // 0.0 to 1.0
  };
  customAttributes: Record<string, unknown>;
  playerControlledCharacter: {
    forwardVector: "left" | "right" | "forward" | "backward";
    rotationSpeed: number;
    movementSpeed: number;
  };
}

export type BuiltInComponentType = keyof BuiltInComponentParams & {};

export type BuiltInComponentData<
  K extends BuiltInComponentType = BuiltInComponentType,
> = {
  readonly uuid: WorldEntityComponentUUID;
  readonly type: K;
  readonly params: BuiltInComponentParams[K];
};

//// Component specific stuff

export type ModelURI_Primitive = `primitive://${string}`;
export type ModelURI_Remote = `http://${string}` | `https://${string}`;
export type ModelURI = ModelURI_Remote | ModelURI_Primitive;

////
//// Runtime constants here — /////
////

/**
 * This is a runtime constant that maps the component type to a string.
 * It is used to ensure that the component type is valid and to make refactoring easier,
 * by avoiding the use of string literals for component types.
 *
 * Note: This is strictly typed such that it can only ever be a dictionaly of
 * exactly every key in BuiltInComponentParams on the left and the right side.
 *
 * E.g. if `BuiltInComponentParams` is `{ foo: { bar: string } }`,
 * then * `BuiltInComponentTypes` can only ever be `{ foo: "foo" }`
 * or you will get a type error.
 */
export const BuiltInComponentTypes: {
  readonly [K in keyof BuiltInComponentParams]: K;
} = {
  worldGroundAsInfinitePlane: "worldGroundAsInfinitePlane",
  modelFromUri: "modelFromUri",
  generationBlob: "generationBlob",
  worldPortal: "worldPortal",
  sprite: "sprite",
  materialOverride: "materialOverride",
  customBehavior: "customBehavior",
  animationLoop: "animationLoop",
  svgPlane: "svgPlane",
  pointParticleEmitter: "pointParticleEmitter",
  customAttributes: "customAttributes",
  playerControlledCharacter: "playerControlledCharacter",
} as const;
