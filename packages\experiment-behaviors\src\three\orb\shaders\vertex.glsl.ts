import { noiseShader } from "./noise.glsl";

export const vertexShader = `${noiseShader}

uniform float time;
uniform float bubbleNoiseScale;
uniform float rippleNoiseScale;
varying vec3 vNormal;
varying vec3 vViewPos;
varying vec3 vWorldPos;

void main(){
  vNormal = normalMatrix * normal;
  // base position
  vec3 pos = position;
  // two noise layers for bubbling + smaller ripples
  float big = snoise(pos * bubbleNoiseScale + time * 0.5) * 0.3;
  float small = snoise(pos * rippleNoiseScale - time * 0.8) * 0.1;
  float displacement = big + small;
  vec3 displaced = pos + normal * displacement;
  vec4 mvPosition = modelViewMatrix * vec4(displaced, 1.0);
  vViewPos = -mvPosition.xyz;
  vWorldPos = (modelMatrix * vec4(displaced, 1.0)).xyz;
  gl_Position = projectionMatrix * mvPosition;
}`;
