name: Deploy Server

on:
  workflow_dispatch: {}
  push:
    branches:
      - main
    paths:
      - .github/workflows/server-deploy.yml
      - apps/server/**
      # consider adding all packages just to be safe
      - packages/logger/**
      - packages/network/**
  pull_request:
    types:
      - opened
      - reopened
      - synchronize
    paths:
      - .github/workflows/server-deploy.yml
      - apps/server/**
      # consider adding all packages just to be safe
      - packages/logger/**
      - packages/network/**

env:
  REGISTRY: europe-west4-docker.pkg.dev
  REGISTRY_PATH: nilo-technologies/nilo

jobs:
  build-container-image:
    runs-on: ubuntu-latest
    concurrency:
      group: server-build-${{ github.sha }}
      cancel-in-progress: true
    permissions:
      contents: read
      id-token: write
    steps:
      - uses: actions/checkout@v4
      - id: auth
        uses: google-github-actions/auth@v2
        with:
          # Note: these are not secrets
          service_account: "<EMAIL>"
          workload_identity_provider: "projects/************/locations/global/workloadIdentityPools/github/providers/nilo-technologies-org"
          token_format: access_token
          access_token_lifetime: 1800s
      - uses: docker/setup-buildx-action@v3
      - uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: oauth2accesstoken
          password: ${{ steps.auth.outputs.access_token }}
      - id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.REGISTRY_PATH }}/nilo-server
          tags: |
            type=schedule
            type=sha
            type=sha,format=long
      - uses: docker/build-push-action@v5
        with:
          file: apps/server/Dockerfile
          context: .
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  deploy-fleet:
    needs: build-container-image
    runs-on: ubuntu-latest
    concurrency:
      group: server-fleet-deploy-${{ github.head_ref || github.ref_name }}
    permissions:
      contents: read
      id-token: write
    steps:
      - uses: actions/checkout@v4
      - uses: google-github-actions/auth@v2
        with:
          # Note: these are not secrets
          service_account: "<EMAIL>"
          workload_identity_provider: "projects/************/locations/global/workloadIdentityPools/github/providers/nilo-technologies-org"
          token_format: access_token
          access_token_lifetime: 300s
      - uses: google-github-actions/get-gke-credentials@v1
        with:
          cluster_name: nilo-eu-west4-cluster
          location: europe-west4
      - uses: pnpm/action-setup@v4
        with:
          version: 10.14.0
      - uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: pnpm
      - run: pnpm install
      - name: Get isolated environment component names
        id: isolated-env
        run: |
          pnpm exec isolated-environments emitGithubActionVars "${{ github.head_ref || github.ref_name }}" | tee $GITHUB_OUTPUT
      - if: github.event_name == 'push' && github.ref_name == 'main'
        name: Update main agones fleet
        run: |
          helm --debug upgrade --install ${{ steps.isolated-env.outputs.fleet }} cloud/helm/nilo-server-fleet \
            --set imageTag=sha-${{ github.sha }} \
            --set-json 'isolatedEnvironment=${{ steps.isolated-env.outputs.json }}' \
            --set autoscaler.buffer=10 \
            --set autoscaler.maxReplicas=300

      - if: github.event_name == 'pull_request'
        name: Update agones fleet for isolated staging environment
        run: |
          helm --debug upgrade --install ${{ steps.isolated-env.outputs.fleet }} cloud/helm/nilo-server-fleet \
            --set imageTag=sha-${{ github.sha }} \
            --set-json 'isolatedEnvironment=${{ steps.isolated-env.outputs.json }}'
