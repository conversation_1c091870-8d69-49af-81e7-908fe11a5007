import { createEcsEngineEvents } from "./createEcsEngineEvents";
import { createSystemContextPooler } from "./system-context/createSystemContextPooler";
import {
  BuiltInComponentData,
  BuiltInComponentType,
} from "./types/BuiltInComponentData";
import {
  EntitiesDataController,
  ErrorsController,
} from "./types/EcsEngineControllers";
// import { createSystemEventMulticaster } from "./utils/createSystemEventMulticaster";

import type { EcsSystem } from "./types/EcsSystem";
import type { WorldEntityComponentData } from "./types/NilusComponentData";
import type { WorldEntityData } from "./types/NilusEntityData";

export type EcsSystemConstructor<T extends EcsSystem> = new () => T;

export function createEcsEngine(
  entitiesDataController: EntitiesDataController,
  errorsController: ErrorsController
) {
  const systems = [] as EcsSystem[];

  const events = createEcsEngineEvents();

  ///////

  const contextPooler = createSystemContextPooler(
    systems,
    entitiesDataController,
    errorsController,
    events
  );

  /**
   * For the time being, we will disallow some things, like adding systems,
   * to be done after the engine is initialized. Just to keep things simpler for now.
   */
  let initialized = false;

  return {
    entitiesDataController,

    getContext: () =>
      contextPooler.getContext({ during: "outside-of-engine-usage" }),

    initialize: () => {
      if (initialized) {
        throw new Error("Engine already initialized");
      }
      initialized = true;

      //// Systems.initialize()

      systems.forEach((system) => {
        if (!system.initialize) {
          return;
        }

        const { systemContext, returnSystemContextToPool } =
          contextPooler.getContext({
            systemName: system.constructor.name,
            during: "system.initialize",
          });

        try {
          system.initialize(systemContext);
        } finally {
          returnSystemContextToPool();
        }
      });

      //// Inform systems about all entities, as if just added

      for (const entity of entitiesDataController.getAllEntities()) {
        const { systemContext, returnSystemContextToPool } =
          contextPooler.getContext({
            // systemName: system.constructor.name, //// TODO: dangit, need to mod the mutlicaster some more
            entityUuid: entity.uuid,
            during: "events.onEntityAdded",
          });

        try {
          events.onEntityAdded.invoke(systemContext, entity);
        } finally {
          returnSystemContextToPool();
        }
      }

      //// Inform systems about all components, as if just added

      for (const entity of entitiesDataController.getAllEntities()) {
        for (const component of Object.values(entity.components)) {
          const { systemContext, returnSystemContextToPool } =
            contextPooler.getContext({
              entityUuid: entity.uuid,
              componentUuid: component.uuid,
              during: "events.onEntityComponentAdded",
            });

          try {
            events.onEntityComponentAdded.invoke(
              systemContext,
              entity,
              component
            );
          } finally {
            returnSystemContextToPool();
          }
        }
      }
    },

    addSystem: <T extends EcsSystem>(SystemClass: EcsSystemConstructor<T>) => {
      if (initialized) {
        throw new Error(
          "Engine already initialized. For the time being, we disallow adding systems after the engine is initialized."
        );
      }

      const system = new SystemClass();
      systems.push(system);
      return system;
    },

    advanceTime(deltaTimeSeconds: number) {
      const { systemContext, returnSystemContextToPool } =
        contextPooler.getContext({
          // systemName: system.constructor.name,
          during: "frame",
        });

      try {
        events.onFrame.invoke(systemContext, deltaTimeSeconds);
      } finally {
        returnSystemContextToPool();
      }
    },

    handleEntityAdded(entityData: WorldEntityData) {
      const { systemContext, returnSystemContextToPool } =
        contextPooler.getContext({
          entityUuid: entityData.uuid,
          during: "events.onEntityAdded",
        });

      try {
        events.onEntityAdded.invoke(systemContext, entityData);
      } finally {
        returnSystemContextToPool();
      }

      for (const componentData of Object.values(entityData.components)) {
        this.handleEntityComponentAdded(entityData, componentData);
      }
    },

    handleEntityRemoved(entityData: WorldEntityData) {
      for (const componentData of Object.values(entityData.components)) {
        this.handleEntityComponentRemoved(entityData, componentData);
      }

      const { systemContext, returnSystemContextToPool } =
        contextPooler.getContext({
          entityUuid: entityData.uuid,
          during: "events.onEntityRemoved",
        });

      try {
        events.onEntityRemoved.invoke(systemContext, entityData);
      } finally {
        returnSystemContextToPool();
      }
    },

    handleEntityTransformUpdated(entityData: WorldEntityData) {
      const { systemContext, returnSystemContextToPool } =
        contextPooler.getContext({
          entityUuid: entityData.uuid,
          during: "events.onEntityTransformUpdated",
        });

      try {
        events.onEntityTransformUpdated.invoke(systemContext, entityData);
      } finally {
        returnSystemContextToPool();
      }
    },

    handleEntityComponentAdded(
      entity: WorldEntityData,
      componentData: WorldEntityComponentData
    ) {
      const { systemContext, returnSystemContextToPool } =
        contextPooler.getContext({
          entityUuid: entity.uuid,
          componentUuid: componentData.uuid,
          during: "events.onEntityComponentAdded",
        });

      try {
        events.onEntityComponentAdded.invoke(
          systemContext,
          entity,
          componentData
        );
        events.onEntityComponentAddedByType.invoke(
          componentData.type as BuiltInComponentType,
          systemContext,
          entity,
          componentData as BuiltInComponentData
        );
      } finally {
        returnSystemContextToPool();
      }
    },

    handleEntityComponentRemoved(
      entity: WorldEntityData,
      componentData: WorldEntityComponentData
    ) {
      const { systemContext, returnSystemContextToPool } =
        contextPooler.getContext({
          entityUuid: entity.uuid,
          componentUuid: componentData.uuid,
          during: "events.onEntityComponentRemoved",
        });

      try {
        events.onEntityComponentRemoved.invoke(
          systemContext,
          entity,
          componentData
        );
        events.onEntityComponentRemovedByType.invoke(
          componentData.type as BuiltInComponentType,
          systemContext,
          entity,
          componentData as BuiltInComponentData
        );
      } finally {
        returnSystemContextToPool();
      }
    },
    handleEntityComponentUpdated(
      entity: WorldEntityData,
      componentData: WorldEntityComponentData
    ) {
      const { systemContext, returnSystemContextToPool } =
        contextPooler.getContext({
          entityUuid: entity.uuid,
          componentUuid: componentData.uuid,
          during: "events.onEntityComponentUpdated",
        });

      try {
        events.onEntityComponentUpdated.invoke(
          systemContext,
          entity,
          componentData
        );
        events.onEntityComponentUpdatedByType.invoke(
          componentData.type as BuiltInComponentType,
          systemContext,
          entity,
          componentData as BuiltInComponentData
        );
      } finally {
        returnSystemContextToPool();
      }
    },

    dispose() {
      systems.forEach((system) => {
        if (!system.dispose) return;

        const { systemContext, returnSystemContextToPool } =
          contextPooler.getContext({
            systemName: system.constructor.name,
            during: "system.dispose",
          });

        try {
          system.dispose(systemContext);
        } finally {
          returnSystemContextToPool();
        }
      });
    },
  };
}

export type EcsEngine = ReturnType<typeof createEcsEngine>;
