class SparseSetEntry<T> {
  value: T;
  index: number = -1;

  constructor(value: T, index: number) {
    this.value = value;
    this.index = index;
  }
}

/** Sparse sets allow storing indexed data in a compact way, without wasting a lot of memory for empty slots */
export class SparseSet<T> {
  private _packed: SparseSetEntry<T>[];
  private _sparse: number[];

  constructor() {
    this._packed = [];
    this._sparse = [];
  }

  insert(index: number, value: T) {
    if (this._sparse.length > index && this._sparse[index] !== -1) {
      this._packed[this._sparse[index]].value = value;
      return false;
    }

    // fill up to index with -1
    for (let i = this._sparse.length; i <= index; i++) {
      this._sparse.push(-1);
    }

    const packedIndex = this._packed.length;
    this._packed[packedIndex] = new SparseSetEntry(value, index);
    this._sparse[index] = packedIndex;
    return true;
  }

  remove(index: number): boolean {
    const packedIndex = this._sparse[index];
    if (packedIndex === -1 || packedIndex == undefined) {
      return false;
    }

    this._packed[packedIndex] = this._packed[this._packed.length - 1];
    if (packedIndex != this._packed.length - 1) {
      this._sparse[this._packed[packedIndex].index] = packedIndex;
    }
    this._packed.pop();
    this._sparse[index] = -1;
    return true;
  }

  length(): number {
    return this._packed.length;
  }

  maxIndex(): number {
    return this._sparse.length - 1;
  }

  get(index: number): T | null {
    if (index >= this._sparse.length) {
      return null;
    }

    const packedIndex = this._sparse[index];
    if (packedIndex === -1) {
      return null;
    }

    return this._packed[packedIndex].value;
  }
}
