type TemplateFunction = (context: Record<string, unknown>) => unknown;

//// TODO: Evaluate the safety of this thing
//// We don't want folks to steal each other's info via funky svg template
export const createTemplateCompiler = () => {
  const cache = new Map<string, TemplateFunction>();

  return {
    compile: (code: string): TemplateFunction => {
      let fn = cache.get(code);
      if (!fn) {
        // Create a function that takes context and returns the evaluated expression
        fn = new Function(
          "context",
          `with (context) { return ${code}; }`
        ) as TemplateFunction;
        cache.set(code, fn);
      }
      return fn;
    },
    dispose: () => {
      cache.clear();
    },
  };
};

export type TemplateCompiler = ReturnType<typeof createTemplateCompiler>;
