import { monotonicFactory, ULIDFactory, ULID } from "ulid";
import { Archetype } from "./archetype";
import { EntityId } from "./entity";
import { Component } from "./component";
import { EntityData } from "./entityData";
import { SystemRunner } from "./system";
import { ServiceStore } from "./serviceStore";

export type ComponentTuple<T> = [Component<T>, T];

class DeferredFunction {
  public func: (world: World) => void;

  constructor(func: (world: World) => void) {
    this.func = func;
  }
}
/**
 * The type of handlers that are notified of newly registered components.
 *
 * Add to worlds with `onRegisterComponent()`.
 */
export type OnRegisterComponent = (component: Component<unknown>) => void;

class AddComponentsToEntity {
  public entity: EntityId;
  public componentValues: ComponentTuple<unknown>[];

  constructor(entity: EntityId, componentValues: ComponentTuple<unknown>[]) {
    this.entity = entity;
    this.componentValues = componentValues;
  }
}

class RemoveComponentFromEntity {
  public entity: EntityId;
  public component: Component<unknown>;

  constructor(entity: EntityId, component: Component<unknown>) {
    this.entity = entity;
    this.component = component;
  }
}

class RemoveEntity {
  public entity: EntityId;

  constructor(entity: EntityId) {
    this.entity = entity;
  }
}

type DeferredAction =
  | AddComponentsToEntity
  | RemoveComponentFromEntity
  | RemoveEntity
  | DeferredFunction;

/**
 * Contains the ECS world, and manages entities and their components
 */
export class World {
  // Archetype of temporary deferred entities. This ensures entities added during query iterations have a valid and consistent entity location, but do not interfere with the main archetypes and any query iterators.
  //
  // This archetype is never visible to queries.
  private _pendingArchetype: Archetype;

  private _archetypes: Map<string, Archetype>;

  // ULID mapping of all entities present in the world
  public entities: Set<EntityId>;

  // Map of entities and their location (archetype and row index)
  public entityLocations: Map<ULID, EntityLocation>;

  // Ensures monotonic ULIDs
  private _ulidFactory: ULIDFactory;

  private _services: ServiceStore;

  /// Registered system runners to ensure they are notified of events, such as entity creation and removal
  private _systemRunners: SystemRunner[] = [];

  /// A list of component registration handlers.
  private _onComponentRegistered: OnRegisterComponent[];

  /// The set of all registered components.
  private _knownComponents: Set<Component<unknown>>;

  // Last generation of archetypes. Changing archetype generation means an archetype was created (or destroyed)
  private _archetypeTick: number;
  // Generation of last entity move
  private _locTick: number;
  // Current tick for any component change
  private _changeTick: number = 0;
  private _archLock: number = 0;

  private _deferredActions: DeferredAction[] = [];

  public get pendingArchetype(): Archetype {
    return this._pendingArchetype;
  }

  public get locTick(): number {
    return this._locTick;
  }

  public get archetypeTick(): number {
    return this._archetypeTick;
  }

  public get changeTick(): number {
    return this._changeTick;
  }

  constructor() {
    this._pendingArchetype = new Archetype([]);
    this._archetypes = new Map();
    this.entities = new Set();
    this.entityLocations = new Map();
    this._ulidFactory = monotonicFactory();
    this._locTick = 0;
    this._archetypeTick = 0;
    this._services = new ServiceStore();
    this._onComponentRegistered = [];
    this._knownComponents = new Set();
  }

  public addService<T extends object>(instance: T): T {
    this._services.add(instance);
    return instance;
  }

  public service<T>(serviceType: new (...args: any[]) => T): T {
    return this._services.get(serviceType);
  }

  public hasService<T>(serviceType: new (...args: any[]) => T): boolean {
    return this._services.has(serviceType);
  }

  public registerSystemRunner(systemRunner: SystemRunner) {
    this._systemRunners.push(systemRunner);
  }

  public archetypes(): IterableIterator<Archetype> {
    return this._archetypes.values();
  }

  getOrCreateArchetype(layout: Component<unknown>[]): Archetype {
    // register sparse components before looking up archetype
    for (const c of layout) {
      if (c.meta?.sparse) {
        this.registerComponent(c);
      }
    }

    // only non-sparse components affect the archetype
    layout = layout.filter((c) => !c.meta?.sparse);

    const key = Array.from(new Set(layout.map((c) => c.KEY)))
      .sort()
      .join("|");

    const existingArchetype = this._archetypes.get(key);
    if (existingArchetype) {
      return existingArchetype;
    }

    // register non-sparse components after checking for existing archetype
    // all component mutations with unregistered components will hit this code
    // therefore existing archetypes will have already registered their components
    for (const c of layout) {
      this.registerComponent(c);
    }

    // When a query is running, we can't create new archetypes
    this.ensureUnlocked();
    this._archetypeTick++;

    const archetype = new Archetype(layout);
    this._archetypes.set(key, archetype);

    return archetype;
  }

  /** Adds a new entity and returns the handle to it.
   *
   * **NOTE**: If called during a query iteration, the entity handle will be reserved but components wont be added, and it won't be visible to queries until the end of the iteration.
   */
  public addEntity(componentValues: ComponentTuple<unknown>[] = []): EntityId {
    const id = this._ulidFactory();
    const entity = id;
    return this.addEntityWithId(entity, componentValues);
  }

  /** Adds an entity with an explicit id, such as a replicated entity.
   *
   * Throws if the entity already exists in the world
   */
  public addEntityWithId(
    entity: EntityId,
    componentValues: ComponentTuple<unknown>[] = []
  ): EntityId {
    return this.spawnEntityWithId(entity, componentValues).id();
  }

  /** Variation of addEntity that returns an reference to the EntityData */
  public spawnEntity(
    componentValues: ComponentTuple<unknown>[] = []
  ): EntityData {
    const id = this._ulidFactory();
    return this.spawnEntityWithId(id, componentValues);
  }

  public spawnEntityWithId(
    entity: EntityId,
    componentValues: ComponentTuple<unknown>[] = []
  ): EntityData {
    if (this.entities.has(entity)) {
      throw new Error(`Entity ${entity} already exists`);
    }

    this.entities.add(entity);

    // Add the entity to the pending archetype. This archetype is never visible to queries, so is safe to add to during an operation
    if (this.isLocked()) {
      const index = this._pendingArchetype.addEntity(entity);
      this._pendingArchetype.assertIsComplete();
      const newLoc = {
        archetype: this._pendingArchetype,
        index,
      };

      this.entityLocations.set(entity, newLoc);

      this.addComponentsDeferred(entity, componentValues);
      return new EntityData(entity, this, newLoc, this.locTick);
    }

    const archetype = this.getOrCreateArchetype(
      componentValues.map(([component, _]) => component)
    );

    const index = archetype.addEntity(entity);
    const newLoc = {
      archetype: archetype,
      index,
    };
    this.entityLocations.set(entity, newLoc);
    this._changeTick++;

    for (const [component, value] of componentValues) {
      if (value === undefined) {
        throw new Error(`Undefined value for component ${component.NAME}`);
      }

      archetype.setComponent(index, component, value, this._changeTick);
    }

    archetype.assertIsComplete();

    const entityData = new EntityData(entity, this, newLoc, this.locTick);
    for (const systemRunner of this._systemRunners) {
      systemRunner.onAddEntity(this, entityData);
    }

    return new EntityData(entity, this, newLoc, this.locTick);
  }

  public isAlive(entity: EntityId): boolean {
    return this.entities.has(entity);
  }

  /** Adds a single component to the entity.
   *
   * **NOTE**: If called during a query iteration, the changes will be deferred and not visible until the end of the iteration.
   *
   * Use `setComponent` instead to modify an existing component immediately.
   */
  public addComponent<T>(entity: EntityId, component: Component<T>, value: T) {
    this.addComponents(entity, [[component, value]]);
  }

  /**
   * Add a set of components to an entity.
   *
   * **NOTE**: If called during a query iteration, the changes will be deferred and not visible until the end of the iteration.
   */
  public addComponents(
    entity: EntityId,
    componentValues: ComponentTuple<unknown>[]
  ) {
    // NOTE: we could defer only if a new layout is required, but for consistency we defer always to not hide bugs that are hard to reproduce
    if (this.isLocked()) {
      this.addComponentsDeferred(entity, componentValues);
      return;
    }

    let entityLoc = this.getLocation(entity);
    const archetype = entityLoc.archetype;

    const newComponents = Array.from(archetype.cells()).map((c) => c.component);
    newComponents.push(...componentValues.map(([component, _]) => component));

    const newArchetype = this.getOrCreateArchetype(newComponents);
    const oldArchetype = entityLoc.archetype;

    // either entity already has all components, or we are adding sparse ones
    if (oldArchetype !== newArchetype) {
      entityLoc = oldArchetype.moveEntity(
        entity,
        this.entityLocations,
        newArchetype
      );
      this._locTick++;
    }

    this._changeTick++;

    let added = false;
    const addedComponents: Component<unknown>[] = [];
    for (const [component, value] of componentValues) {
      if (value === undefined) {
        throw new Error(`Undefined value for component ${component.NAME}`);
      }

      const newComponent = newArchetype.setComponent(
        entityLoc.index,
        component,
        value,
        this._changeTick
      );

      if (newComponent) {
        added = true;
        addedComponents.push(component);
      }
    }

    oldArchetype.assertIsComplete();
    newArchetype.assertIsComplete();

    if (added) {
      const entityData = new EntityData(entity, this, entityLoc, this.locTick);

      for (const systemRunner of this._systemRunners) {
        systemRunner.onEntityMoved(
          this,
          entityData,
          oldArchetype,
          entityLoc,
          addedComponents,
          false
        );
      }
    }
  }

  public addComponentsDeferred(
    entity: EntityId,
    componentValues: ComponentTuple<unknown>[]
  ) {
    this._deferredActions.push(
      new AddComponentsToEntity(entity, componentValues)
    );
  }

  public removeComponentDeferred(
    entity: EntityId,
    component: Component<unknown>
  ) {
    this._deferredActions.push(
      new RemoveComponentFromEntity(entity, component)
    );
  }

  public removeEntityDeferred(entity: EntityId) {
    this._deferredActions.push(new RemoveEntity(entity));
  }

  /**
   * Removes a component from an entity.
   *
   * **NOTE**: If called during a query iteration, the changes will be deferred and not visible until the end of the iteration.
   */
  public removeComponent<T>(entity: EntityId, component: Component<T>) {
    if (this.isLocked()) {
      this.removeComponentDeferred(entity, component);
      return;
    }

    let entityLoc = this.getLocation(entity);

    const archetype = entityLoc.archetype;

    const newComponents = Array.from(archetype.cells()).map((c) => c.component);
    const componentIndex = newComponents.indexOf(component);
    if (!component.meta?.sparse && componentIndex === -1) {
      return;
    }

    const oldArchetype = entityLoc.archetype;
    let newArchetype;
    newComponents.splice(componentIndex, 1);

    this._locTick++;
    newArchetype = this.getOrCreateArchetype(newComponents);
    if (componentIndex !== -1) {
      entityLoc = oldArchetype.moveEntity(
        entity,
        this.entityLocations,
        newArchetype
      );
    } else {
      newArchetype = oldArchetype;
    }

    if (component.meta?.sparse) {
      if (!newArchetype.removeSparseComponent(entityLoc.index, component)) {
        return;
      }
    }

    oldArchetype.assertIsComplete();
    newArchetype.assertIsComplete();

    // Acquire a lock to ensure no system removes an entity and thus subverts the filters validity,
    // leading to missing components as others systems may expect it to still be present
    this.acquireArchetypeLock();
    try {
      const entityData = new EntityData(entity, this, entityLoc, this.locTick);
      for (const systemRunner of this._systemRunners) {
        systemRunner.onEntityMoved(
          this,
          entityData,
          oldArchetype,
          entityLoc,
          [component],
          true
        );
      }
    } finally {
      this.releaseArchetypeLock();
    }
  }

  public removeEntity(entity: EntityId) {
    const entityLoc = this.getLocation(entity);

    if (this.isLocked()) {
      this.removeEntityDeferred(entity);
      return;
    }

    const oldArchetype = entityLoc.archetype;
    this._locTick++;
    const entityData = new EntityData(entity, this, entityLoc, this.locTick);
    this.acquireArchetypeLock();
    try {
      for (const systemRunner of this._systemRunners) {
        systemRunner.onRemoveEntity(this, entityData);
      }

      oldArchetype.removeEntity(entity, this.entityLocations);
      this.entities.delete(entity);
      this.entityLocations.delete(entity);

      oldArchetype.assertIsComplete();
    } finally {
      this.releaseArchetypeLock();
    }
  }

  public getComponent<T>(
    entity: EntityId,
    componentType: Component<T>
  ): T | null {
    const entityLoc = this.getLocation(entity);

    const value = entityLoc.archetype.getComponent(
      entityLoc.index,
      componentType
    );

    if (value === undefined) {
      throw new Error(
        `Mismatched component type. Expected ${componentType.NAME}, archetype has ${Array.from(
          entityLoc.archetype.cells()
        )
          .map((c) => c.component.NAME)
          .join(", ")}, sparse: ${Array.from(entityLoc.archetype.sparseCells())
          .map((c) => c.component.NAME)
          .join(", ")}`
      );
    }

    return value as T;
  }

  /** Set the value of an already present component
   *
   * Unlike add, this will not defer the change during a query
   */
  public setComponent<T>(entity: EntityId, component: Component<T>, value: T) {
    if (value === undefined) {
      throw new Error(`Undefined value for component ${component.NAME}`);
    }

    const entityLoc = this.getLocation(entity);

    if (
      !entityLoc.archetype.hasComponentForEntity(entityLoc.index, component)
    ) {
      throw new Error(
        `Entity ${entity} does not have component ${component.NAME}`
      );
    }

    this._changeTick++;

    entityLoc.archetype.setComponent(
      entityLoc.index,
      component,
      value,
      this._changeTick
    );
  }

  /** Set the value of an already present component
   *
   * Unlike add, this will not defer the change during a query
   */
  public setComponentByLocation<T>(
    entityLoc: EntityLocation,
    component: Component<T>,
    value: T
  ) {
    if (value === undefined) {
      throw new Error(`Undefined value for component ${component.NAME}`);
    }

    if (
      !entityLoc.archetype.hasComponentForEntity(entityLoc.index, component)
    ) {
      throw new Error(
        `Entity ${entityLoc.archetype.entities[entityLoc.index]} does not have component ${component.NAME}`
      );
    }

    this._changeTick++;

    entityLoc.archetype.setComponent(
      entityLoc.index,
      component,
      value,
      this._changeTick
    );
  }

  public markDirty(entity: EntityId, component: Component<unknown>) {
    const entityLoc = this.getLocation(entity);
    entityLoc.archetype.markDirty(this._changeTick, entityLoc.index, component);
  }

  /** Returns true if the entity has the component */
  public hasComponent<T>(entity: EntityId, component: Component<T>): boolean {
    const entityLoc = this.getLocation(entity);
    return entityLoc.archetype.hasComponentForEntity(
      entityLoc.index,
      component
    );
  }

  public entityData(entity: EntityId): EntityData | null {
    const entityLoc = this.entityLocations.get(entity);
    if (!entityLoc) {
      return null;
    }
    return new EntityData(entity, this, entityLoc, this.locTick);
  }

  public getLocation(entity: EntityId): EntityLocation {
    const entityLoc = this.entityLocations.get(entity);
    if (!entityLoc) {
      throw new Error(`Entity ${entity} not found`);
    }
    return entityLoc;
  }

  public ensureUnlocked(): void {
    if (this._archLock > 0) {
      throw new Error(
        "Archetype lock present. World archetype modification not allowed"
      );
    }
  }

  /** When iterating over entities, the world is locked to prevent modifying archetypes or moving entity indices, to ensure the iteration is consistent
   *
   * All operations that modify the structure of the world are automatically deferred until the last acquired lock is released
   */
  public isLocked(): boolean {
    return this._archLock > 0;
  }

  /** Acquires the archetype lock, preventing modifications to archetypes or moving entity indices
   *
   * Lock is reentrant and will not be released until the last lock is released
   */
  public acquireArchetypeLock(): void {
    this._archLock++;
  }

  /** Releases one level of archetype lock, allowing modifications to archetypes or moving entity indices
   *
   * If the lock is released and there are deferred actions, they will be flushed
   */
  public releaseArchetypeLock(): void {
    if (this._archLock === 0) {
      throw new Error("Archetype lock not acquired");
    }

    this._archLock--;

    if (this._archLock === 0) {
      this.flushDeferredActions();
    }
  }

  /** Wait to execute an action on the world until the world is not locked.
   *
   * If the world is not locked, the action is executed immediately.
   * */
  public defer(func: (world: World) => void) {
    if (this.isLocked()) {
      this._deferredActions.push(new DeferredFunction(func));
    } else {
      func(this);
    }
  }

  public flushDeferredActions(): void {
    for (const action of this._deferredActions) {
      switch (action.constructor) {
        case AddComponentsToEntity: {
          const add = action as AddComponentsToEntity;
          if (!this.isAlive(add.entity)) {
            continue;
          }

          this.addComponents(add.entity, add.componentValues);
          break;
        }
        case RemoveComponentFromEntity: {
          const remove = action as RemoveComponentFromEntity;
          if (!this.isAlive(remove.entity)) {
            continue;
          }

          this.removeComponent(remove.entity, remove.component);
          break;
        }
        case RemoveEntity: {
          const removeEntity = action as RemoveEntity;
          if (!this.isAlive(removeEntity.entity)) {
            continue;
          }

          this.removeEntity(removeEntity.entity);
          break;
        }
        case DeferredFunction:
          (action as DeferredFunction).func(this);
          break;
        default:
          throw new Error(
            "Unexpected deferred action: " + action.constructor.name
          );
      }
    }
    this._deferredActions = [];
  }

  /**
   * Adds a handler for notifications of when components are first registered.
   * Immediately calls the handler on all already-known components.
   */
  public onRegisterComponent(handler: OnRegisterComponent) {
    // add the handler to the running set
    this._onComponentRegistered.push(handler);

    // invoke it on all already-known components
    for (const component of this._knownComponents) {
      handler(component);
    }
  }

  /**
   * If this component has not been seen before, notify all component registration
   * handlers of a new component.
   */
  public registerComponent(component: Component<unknown>) {
    // skip if the component has been seen before
    if (!this._knownComponents.has(component)) {
      // track the new component
      this._knownComponents.add(component);

      // invoke all handlers on this component
      for (const handler of this._onComponentRegistered) {
        // TODO: defer running handlers if locked or in hot code?
        handler(component);
      }
    }
  }
}

/** Stores the entities position within its archetype */
export class EntityLocation {
  archetype: Archetype;
  index: number;

  constructor(archetype: Archetype, index: number) {
    this.archetype = archetype;
    this.index = index;
  }
}
