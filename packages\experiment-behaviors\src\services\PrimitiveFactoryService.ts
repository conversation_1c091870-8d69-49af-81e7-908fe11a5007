import {
  BoxGeometry,
  BufferGeometry,
  ConeGeometry,
  CylinderGeometry,
  DodecahedronGeometry,
  IcosahedronGeometry,
  Mesh,
  MeshStandardMaterial,
  OctahedronGeometry,
  PlaneGeometry,
  RingGeometry,
  SphereGeometry,
  TetrahedronGeometry,
  TorusGeometry,
  TorusKnotGeometry,
  CapsuleGeometry,
  ExtrudeGeometry,
  Shape,
} from "three";
import { ModelURI_Primitive } from "@nilo/experiment-behaviors/types/BuiltInComponentData";

const materialForPrimitives = new MeshStandardMaterial({
  color: 0x203060,
  metalness: 0.8,
  roughness: 0.5,
});

function createPrimitiveFactoryService() {
  function isPrimitiveUri(uri: string): uri is ModelURI_Primitive {
    return uri.startsWith("primitive://");
  }

  function createPrimitiveGeometry(
    primitiveUri: ModelURI_Primitive
  ): BufferGeometry | undefined {
    const type = primitiveUri.substring("primitive://".length);

    switch (type) {
      case "box":
        return new BoxGeometry();
      case "sphere":
        return new SphereGeometry(0.5, 8, 6);
      case "cone":
        return new ConeGeometry(0.5, 1, 8);
      case "cylinder":
        return new CylinderGeometry(0.5, 0.5, 1, 12);
      case "dodecahedron":
        return new DodecahedronGeometry(0.5);
      case "icosahedron":
        return new IcosahedronGeometry(0.5);
      case "octahedron":
        return new OctahedronGeometry(0.5);
      case "plane":
        return new PlaneGeometry(1, 1);
      case "ring":
        return new RingGeometry(0.25, 0.5, 12);
      case "tetrahedron":
        return new TetrahedronGeometry(0.5);
      case "torus":
        return new TorusGeometry(0.5, 0.2, 6, 12);
      case "torusknot":
        return new TorusKnotGeometry(0.5, 0.1, 100, 16);
      case "capsule":
        return new CapsuleGeometry(0.5, 1, 0.5);
      case "wedge": {
        const shape = new Shape();
        shape.moveTo(0, 0);
        shape.lineTo(1, 0);
        shape.lineTo(0.5, 1);
        shape.lineTo(0, 0);
        return new ExtrudeGeometry(shape, {
          steps: 1,
          depth: 0.5,
          bevelEnabled: false,
        });
      }
      default:
        console.warn("🔶 Unknown primitive type:", type);
        return undefined;
    }
  }

  function createPrimitive(uri: ModelURI_Primitive): Mesh | undefined {
    const geometry = createPrimitiveGeometry(uri);
    if (!geometry) {
      return undefined;
    }
    return new Mesh(geometry, materialForPrimitives);
  }

  return {
    isPrimitiveUri,
    createPrimitive,
  };
}

export type PrimitiveFactoryService = ReturnType<
  typeof createPrimitiveFactoryService
>;
export const primitiveFactoryService = createPrimitiveFactoryService();
