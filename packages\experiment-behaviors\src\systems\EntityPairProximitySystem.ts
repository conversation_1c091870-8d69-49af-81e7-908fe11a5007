import { createMulticaster } from "@nilo/experiment-behaviors/utils/createMulticaster";
import { getManhattanDistance } from "@nilo/experiment-behaviors/utils/used-by-systems/getManhattanDistance";

import type { EcsSystemContext } from "@nilo/experiment-behaviors/system-context/createSystemContext";
import type { EcsSystem } from "@nilo/experiment-behaviors/types/EcsSystem";
import type {
  WorldEntityData,
  WorldEntityUUID,
} from "@nilo/experiment-behaviors/types/NilusEntityData";

type ProximityCallback = () => void;

type ProximityPair = {
  entityA: WorldEntityUUID;
  entityB: WorldEntityUUID;
  range: number;
  onEnter: ProximityCallback;
  onLeave: ProximityCallback;
};

type ProximityState = {
  isInRange: boolean;
  pair: ProximityPair;
};

export class EntityPairProximitySystem implements EcsSystem {
  private readonly proximityPairs = new Map<string, ProximityState>();
  private readonly onDispose = createMulticaster();

  initialize(context: EcsSystemContext) {
    // Track entity removal to clean up proximity pairs
    context.events.onEntityRemoved.add((_context, entityData) => {
      this.onEntityRemoved(entityData.uuid);
    });

    // Track entity transform updates to check proximity
    context.events.onEntityTransformUpdated.add((context, entityData) => {
      this.onEntityTransformUpdated(context, entityData);
    });
  }

  private getPairKey(a: WorldEntityUUID, b: WorldEntityUUID): string {
    // Ensure consistent ordering of UUIDs for the key
    return [a, b].sort().join(":");
  }

  private onEntityRemoved(entityUuid: WorldEntityUUID) {
    // Remove all proximity pairs involving this entity
    for (const [key, state] of this.proximityPairs.entries()) {
      if (
        state.pair.entityA === entityUuid ||
        state.pair.entityB === entityUuid
      ) {
        // If entities were in range, trigger leave callback
        if (state.isInRange) {
          state.pair.onLeave();
        }
        this.proximityPairs.delete(key);
      }
    }
  }

  private onEntityTransformUpdated(
    context: EcsSystemContext,
    entityData: WorldEntityData
  ) {
    // Check all pairs involving this entity
    for (const [_key, state] of this.proximityPairs.entries()) {
      const { entityA, entityB, range, onEnter, onLeave } = state.pair;

      // Skip if this entity isn't part of this pair
      if (entityData.uuid !== entityA && entityData.uuid !== entityB) continue;

      // Get the other entity's data
      const otherEntityUuid = entityData.uuid === entityA ? entityB : entityA;
      const otherEntity = context.getEntityData(otherEntityUuid);
      if (!otherEntity) continue;

      // Calculate current distance
      const distance = getManhattanDistance(
        entityData.transform,
        otherEntity.transform
      );
      const isInRange = distance <= range;

      // Handle state changes
      if (isInRange && !state.isInRange) {
        state.isInRange = true;
        onEnter();
      } else if (!isInRange && state.isInRange) {
        state.isInRange = false;
        onLeave();
      }
    }
  }

  public getApi(context: EcsSystemContext) {
    return {
      onEnterRange: (
        entityA: WorldEntityUUID,
        entityB: WorldEntityUUID,
        range: number,
        onEnter: ProximityCallback,
        onLeave: ProximityCallback
      ) => {
        const key = this.getPairKey(entityA, entityB);

        // Remove existing pair if any
        const existing = this.proximityPairs.get(key);
        if (existing) {
          if (existing.isInRange) {
            existing.pair.onLeave();
          }
          this.proximityPairs.delete(key);
        }

        // Create new pair
        const pair: ProximityPair = {
          entityA,
          entityB,
          range,
          onEnter,
          onLeave,
        };

        // Check initial state
        const entityAData = context.getEntityData(entityA);
        const entityBData = context.getEntityData(entityB);

        if (!entityAData || !entityBData) {
          console.warn(
            "🎯 EntityProximitySystem: One or both entities not found"
          );
          return;
        }

        const initialDistance = getManhattanDistance(
          entityAData.transform,
          entityBData.transform
        );
        const isInRange = initialDistance <= range;

        this.proximityPairs.set(key, { isInRange, pair });

        // Trigger initial enter if in range
        if (isInRange) {
          onEnter();
        }
      },

      clearProximityPair: (
        entityA: WorldEntityUUID,
        entityB: WorldEntityUUID
      ) => {
        const key = this.getPairKey(entityA, entityB);
        const state = this.proximityPairs.get(key);

        if (state) {
          if (state.isInRange) {
            state.pair.onLeave();
          }
          this.proximityPairs.delete(key);
        }
      },

      clearAllProximityPairs: () => {
        for (const state of this.proximityPairs.values()) {
          if (state.isInRange) {
            state.pair.onLeave();
          }
        }
        this.proximityPairs.clear();
      },
    };
  }

  dispose() {
    // Trigger leave callbacks for all active pairs
    for (const state of this.proximityPairs.values()) {
      if (state.isInRange) {
        state.pair.onLeave();
      }
    }
    this.proximityPairs.clear();
    this.onDispose.invoke();
  }
}
