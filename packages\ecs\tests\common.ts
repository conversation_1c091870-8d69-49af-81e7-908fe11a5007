import { Component } from "../src/component";
import { EntityBuilder, EntityData } from "../src/entityData";
import { Behavior, BehaviorConstructor, NameBehavior } from "../src/behavior";

export type Vector2 = {
  x: number;
  y: number;
};

export type Vector3 = {
  x: number;
  y: number;
  z: number;
};

export const Position = new Component<Vector2>("position").withDescription(
  "position of the entity"
);

export const Color = new Component<Vector3>("color");

export const Velocity = new Component<Vector2>("velocity").withDescription(
  "velocity of the entity"
);

export const SparseMarker = new Component<string>("sparse_marker")
  .withDescription("marker component for sparse components")
  .withSparse();

export const OtherMarker = new Component<string>("other_marker")
  .withDescription("other marker component for sparse components")
  .withSparse();

export class PositionBehavior implements Behavior {
  public readonly position: Vector2;

  constructor(position?: Vector2) {
    this.position = position ?? { x: 0, y: 0 };
  }

  dependencies(): BehaviorConstructor[] {
    return [];
  }

  getFromEntity(entity: EntityData): this {
    const position = entity.getComponent(Position) ?? this.position;
    return new PositionBehavior(position) as this;
  }

  addToEntity(entity: EntityBuilder): void {
    entity.addComponents([[Position, this.position]]);
  }
}

export class VelocityBehavior implements Behavior {
  public readonly velocity: Vector2;

  constructor(velocity?: Vector2) {
    this.velocity = velocity ?? { x: 0, y: 0 };
  }

  dependencies(): BehaviorConstructor[] {
    return [PositionBehavior];
  }

  getFromEntity(entity: EntityData): this {
    const velocity = entity.getComponent(Velocity) ?? this.velocity;
    return new VelocityBehavior(velocity) as this;
  }

  addToEntity(entity: EntityBuilder): void {
    entity.addComponents([[Velocity, this.velocity]]);
  }
}

export class PhysicalObjectBehavior implements Behavior {
  constructor() {}

  dependencies(): BehaviorConstructor[] {
    return [VelocityBehavior, NameBehavior];
  }

  getFromEntity(_entity: EntityData): this {
    return new PhysicalObjectBehavior() as this;
  }

  addToEntity(_entity: EntityBuilder): void {
    return;
  }
}
