import {
  adjectives as unfilteredAdjectives,
  colors as unfilteredColors,
  animals as unfilteredAnimals,
} from "unique-names-generator";

const potentiallyNsfwAdjectives = [
  "bloody", // Can be a swear word or refer to gore
  "fat", // Often used as an insult
  "gay", // Can be used as a slur, though not inherently nsfw
  "hot", // Can have sexual connotations
  "naked", // Refers to nudity
  "nasty", // Can have sexual or offensive connotations
  "oral", // Can refer to oral sex
  "sexual", // Explicitly refers to sex
  "stupid", // An insult
  "thirsty", // Can have sexual connotations
  "ugly", // An insult
  "xenophobic", // An insult
  "wet", // Can have sexual connotations
  ////
  "capitalist",
  "christian",
  "collective",
  "colonial",
  "communist",
  "conservative",
  "constitutional",
  "democratic",
  "electoral",
  "federal",
  "feminist",
  "fiscal",
  "imperial",
  "labour",
  "left",
  "legislative",
  "liberal",
  "marxist",
  "moderate",
  "national",
  "orthodox",
  "parliamentary",
  "political",
  "presidential",
  "progressive",
  "protestant",
  "racial",
  "radical",
  "revolutionary",
  "right",
  "socialist",
  "soviet",
  "tory",
];

const potentiallyNsfwAnimals = [
  "booby", // Type of seagull, but also used for breasts
  "beaver", // Type of animal, but also has double meaning
];

const adjectives = unfilteredAdjectives.filter(
  (adjective) => !potentiallyNsfwAdjectives.includes(adjective)
);
const animals = unfilteredAnimals.filter(
  (animal) => !potentiallyNsfwAnimals.includes(animal)
);
const colors = unfilteredColors;

export const sfwDictionaries = {
  adjectives,
  animals,
  colors,
};
